import requests
from bs4 import BeautifulSoup
import re

def debug_new_pmc_logic(url):
    """Debug logic mới cho pmc-content-html"""
    print(f"🔍 Debug logic mới từ: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Tìm tất cả thẻ có class "pmc-content-html"
        pmc_elements = soup.find_all(class_="pmc-content-html")
        print(f"📄 Tìm thấy {len(pmc_elements)} thẻ có class 'pmc-content-html'")
        
        tong_hop = "Không có"
        pmc_columns = {}
        
        if len(pmc_elements) == 1:
            print("\n📋 TH1: Chỉ có 1 thẻ pmc-content-html")
            tong_hop = pmc_elements[0].get_text(strip=True)
            if len(tong_hop) > 200:  # Giới hạn cho test
                tong_hop = tong_hop[:200] + "..."
            print(f"✅ Tổng hợp: {tong_hop}")
            
        elif len(pmc_elements) > 1:
            print(f"\n📋 TH2: Có {len(pmc_elements)} thẻ pmc-content-html")
            
            for i, element in enumerate(pmc_elements):
                print(f"\n--- Thẻ thứ {i+1} ---")
                
                # Tìm thẻ h(x) trong element này
                h_tags = element.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                print(f"🔍 Tìm thấy {len(h_tags)} thẻ h(x)")
                
                if h_tags:
                    # Lấy text của thẻ h(x) đầu tiên làm tên cột
                    column_name = h_tags[0].get_text(strip=True)
                    print(f"🏷️  Tên cột: {repr(column_name)}")
                    
                    # Lấy toàn bộ text của element
                    full_text = element.get_text(strip=True)
                    print(f"📝 Text đầy đủ: {repr(full_text[:100])}...")
                    
                    # Loại bỏ text của tất cả thẻ h(x) khỏi nội dung
                    content_text = full_text
                    for h_tag in h_tags:
                        h_text = h_tag.get_text(strip=True)
                        content_text = content_text.replace(h_text, "", 1)
                        print(f"🗑️  Loại bỏ: {repr(h_text)}")
                    
                    # Làm sạch text
                    content_text = content_text.strip()
                    content_text = re.sub(r'\s+', ' ', content_text)
                    
                    if len(content_text) > 200:  # Giới hạn cho test
                        content_text = content_text[:200] + "..."
                    
                    print(f"✅ Nội dung sau khi xử lý: {repr(content_text[:100])}...")
                    
                    # Lưu vào dictionary
                    if column_name and content_text:
                        pmc_columns[column_name] = content_text
                        print(f"💾 Đã lưu cột: {column_name}")
                    else:
                        print(f"❌ Không lưu được (tên hoặc nội dung trống)")
                else:
                    print(f"❌ Không tìm thấy thẻ h(x)")
        
        print(f"\n📊 Kết quả cuối cùng:")
        print(f"   Tổng hợp: {tong_hop}")
        print(f"   Số cột PMC: {len(pmc_columns)}")
        
        # Sắp xếp theo alphabet
        sorted_columns = sorted(pmc_columns.items())
        for column_name, content in sorted_columns:
            print(f"   {column_name}: {content[:50]}...")
        
        return tong_hop, pmc_columns
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return None, None

if __name__ == "__main__":
    # Test với sản phẩm có nhiều thẻ pmc-content-html
    test_urls = [
        "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html",
        "https://www.pharmacity.vn/genesign-test-respiratory-combo-antigen-rapid-20-test-hop.html"
    ]
    
    for i, test_url in enumerate(test_urls):
        print(f"\n{'='*80}")
        print(f"TEST {i+1}")
        tong_hop, pmc_columns = debug_new_pmc_logic(test_url)
        
        if pmc_columns is not None:
            print(f"\n🎯 Tổng cộng sẽ có {18 + len(pmc_columns) + 1} cột:")
            print(f"   - 18 cột cơ bản")
            print(f"   - {len(pmc_columns)} cột PMC động")
            print(f"   - 1 cột Tổng hợp")
        else:
            print(f"\n❌ Không lấy được dữ liệu!")
