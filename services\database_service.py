# ===== DATABASE SERVICE =====

from config.database import (
    get_db_connection, 
    check_table_exists, 
    get_table_structure, 
    get_table_count, 
    get_sample_data
)

class DatabaseService:
    """Service để quản lý database operations"""
    
    def __init__(self):
        self.connection = None
    
    def connect(self):
        """Kết nối database"""
        print("🔗 Đang kết nối MySQL...")
        self.connection = get_db_connection()
        return self.connection
    
    def disconnect(self):
        """Đóng kết nối database"""
        if self.connection:
            self.connection.close()
            print("🔌 Đã đóng kết nối MySQL")
    
    def validate_table(self, table_name):
        """Kiểm tra và validate table"""
        print(f"🔧 Kiểm tra table {table_name}...")
        
        if not check_table_exists(self.connection, table_name):
            print(f"❌ Table {table_name} chưa tồn tại!")
            print("💡 Vui lòng tạo table trước hoặc kiểm tra tên database/table")
            return False
        
        print(f"✅ Table {table_name} đã tồn tại!")
        
        # Hiển thị cấu trúc table
        columns = get_table_structure(self.connection, table_name)
        print(f"📋 Cấu trúc table {table_name}:")
        for col in columns:
            print(f"  - {col['Field']}: {col['Type']}")
        
        return True
    
    def show_table_info(self, table_name):
        """Hiển thị thông tin table"""
        print(f"🔍 Kiểm tra dữ liệu hiện có trong table {table_name}...")
        
        try:
            count = get_table_count(self.connection, table_name)
            print(f"📊 Hiện có {count} records trong table {table_name}")
            
            if count > 0:
                samples = get_sample_data(self.connection, table_name, limit=1)
                if samples:
                    print("📋 Mẫu dữ liệu:")
                    for key, value in samples[0].items():
                        print(f"  - {key}: {value}")
        except Exception as e:
            print(f"❌ Lỗi khi kiểm tra dữ liệu: {e}")
    
    def get_connection(self):
        """Lấy connection hiện tại"""
        if not self.connection:
            raise Exception("Chưa kết nối database. Hãy gọi connect() trước.")
        return self.connection
