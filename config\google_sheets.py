# ===== GOOGLE SHEETS CONFIGURATION =====

import gspread
from oauth2client.service_account import ServiceAccountCredentials

# Google Sheets config
SHEET_CONFIG = {
    'sheet_id': "1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8",
    'credentials_file': "medical-crawl-2024-013b6faaa588.json",
    'scope': [
        "https://spreadsheets.google.com/feeds", 
        "https://www.googleapis.com/auth/drive"
    ]
}

def get_google_sheets_client():
    """Tạo kết nối Google Sheets"""
    try:
        creds = ServiceAccountCredentials.from_json_keyfile_name(
            SHEET_CONFIG['credentials_file'], 
            SHEET_CONFIG['scope']
        )
        client = gspread.authorize(creds)
        print("✅ Đã kết nối Google Sheets thành công!")
        return client
    except Exception as e:
        print(f"❌ Lỗi kết nối Google Sheets: {e}")
        raise e

def get_worksheet(client, sheet_name):
    """Lấy worksheet từ Google Sheets"""
    try:
        sheet = client.open_by_key(SHEET_CONFIG['sheet_id']).worksheet(sheet_name)
        return sheet
    except Exception as e:
        print(f"❌ Lỗi lấy worksheet '{sheet_name}': {e}")
        raise e

def get_sheet_data(sheet, start_row=2, max_rows=None):
    """Lấy dữ liệu từ Google Sheets"""
    print("📋 Đang lấy dữ liệu từ Google Sheets...")
    
    try:
        # Lấy tất cả dữ liệu từ sheet
        all_data = sheet.get_all_values()
        
        # Tính toán số dòng thực tế có data (bỏ qua header)
        total_rows_with_header = len(all_data)
        total_data_rows = total_rows_with_header - (start_row - 1)
        
        print(f"📊 Sheet có tổng cộng {total_rows_with_header} dòng (bao gồm header)")
        print(f"📊 Số dòng data thực tế: {total_data_rows} dòng")

        # Bỏ qua header
        data_rows = all_data[start_row-1:]

        # Giới hạn số lượng dòng nếu cần
        if max_rows:
            if max_rows > total_data_rows:
                print(f"⚠️  MAX_ROWS ({max_rows}) lớn hơn số dòng data thực tế ({total_data_rows})")
                print(f"📋 Sẽ xử lý TẤT CẢ {total_data_rows} dòng data có sẵn")
            else:
                print(f"📋 Giới hạn xử lý {max_rows} dòng đầu tiên từ {total_data_rows} dòng data")
                data_rows = data_rows[:max_rows]
        else:
            print(f"📋 Sẽ xử lý TẤT CẢ {total_data_rows} dòng data")

        actual_rows_to_process = len(data_rows)
        print(f"✅ Đã lấy {actual_rows_to_process} dòng dữ liệu từ Google Sheets để xử lý")
        
        return data_rows
    except Exception as e:
        print(f"❌ Lỗi khi lấy dữ liệu từ Google Sheets: {e}")
        raise e
