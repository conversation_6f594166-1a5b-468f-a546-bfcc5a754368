import requests
from bs4 import BeautifulSoup
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def init_browser():
    """Khởi tạo browser v<PERSON><PERSON> cấu hình tối ưu"""
    print("🚀 Khởi tạo Chrome browser...")
    service = Service(ChromeDriverManager().install())
    
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--log-level=3")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    print("✅ Chrome browser đã sẵn sàng!")
    return driver

def test_pharmacy_website(name, url, driver=None):
    """Test một website nhà thuốc"""
    print(f"\n{'='*80}")
    print(f"🏥 TEST WEBSITE: {name}")
    print(f"🌐 URL: {url}")
    print(f"{'='*80}")
    
    # Test với requests trước
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        print("📡 Test với requests...")
        response = requests.get(url, headers=headers, timeout=15, verify=False)
        print(f"  📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            title = soup.find('title')
            if title:
                print(f"  📄 Title: {title.get_text()[:100]}")
            
            # Tìm link sản phẩm
            links = soup.find_all('a', href=True)
            product_links = []
            
            for link in links[:100]:  # Check 100 link đầu
                href = link.get('href')
                text = link.get_text(strip=True)
                
                if href and ('.html' in href or '/thuoc/' in href or '/san-pham/' in href or '/product/' in href):
                    if href.startswith('/'):
                        full_url = url + href
                    elif href.startswith('http'):
                        full_url = href
                    else:
                        continue
                    
                    if full_url not in product_links:
                        product_links.append((text[:50], full_url))
            
            print(f"  🔗 Tìm thấy {len(product_links)} link sản phẩm tiềm năng")
            
            if product_links:
                print(f"  📋 Ví dụ sản phẩm:")
                for i, (text, link) in enumerate(product_links[:5]):
                    print(f"    {i+1}. {text}: {link}")
            
            # Test với Selenium nếu có
            if driver:
                try:
                    print("\n🤖 Test với Selenium...")
                    driver.get(url)
                    time.sleep(5)
                    
                    selenium_title = driver.title
                    print(f"  📄 Selenium title: {selenium_title[:100]}")
                    
                    # Tìm sản phẩm với Selenium
                    selenium_soup = BeautifulSoup(driver.page_source, 'html.parser')
                    selenium_links = selenium_soup.find_all('a', href=True)
                    
                    selenium_products = []
                    for link in selenium_links[:100]:
                        href = link.get('href')
                        if href and ('.html' in href or '/thuoc/' in href or '/san-pham/' in href):
                            if href.startswith('/'):
                                full_url = url + href
                            elif href.startswith('http'):
                                full_url = href
                            else:
                                continue
                            
                            if full_url not in [p[1] for p in selenium_products]:
                                selenium_products.append((link.get_text(strip=True)[:50], full_url))
                    
                    print(f"  🔗 Selenium tìm thấy {len(selenium_products)} link sản phẩm")
                    
                except Exception as e:
                    print(f"  ❌ Lỗi Selenium: {e}")
            
            return {
                'accessible': True,
                'product_count': len(product_links),
                'sample_products': product_links[:5]
            }
        else:
            print(f"  ❌ Không thể truy cập (status: {response.status_code})")
            return {'accessible': False, 'error': f'Status {response.status_code}'}
            
    except Exception as e:
        print(f"  ❌ Lỗi requests: {e}")
        
        # Thử với Selenium nếu requests thất bại
        if driver:
            try:
                print("\n🤖 Thử với Selenium...")
                driver.get(url)
                time.sleep(10)
                
                selenium_title = driver.title
                print(f"  📄 Selenium title: {selenium_title[:100]}")
                
                if "404" not in selenium_title.lower() and "error" not in selenium_title.lower():
                    selenium_soup = BeautifulSoup(driver.page_source, 'html.parser')
                    selenium_links = selenium_soup.find_all('a', href=True)
                    
                    selenium_products = []
                    for link in selenium_links[:100]:
                        href = link.get('href')
                        if href and ('.html' in href or '/thuoc/' in href or '/san-pham/' in href):
                            if href.startswith('/'):
                                full_url = url + href
                            elif href.startswith('http'):
                                full_url = href
                            else:
                                continue
                            
                            if full_url not in [p[1] for p in selenium_products]:
                                selenium_products.append((link.get_text(strip=True)[:50], full_url))
                    
                    print(f"  🔗 Selenium tìm thấy {len(selenium_products)} link sản phẩm")
                    
                    if selenium_products:
                        print(f"  📋 Ví dụ sản phẩm:")
                        for i, (text, link) in enumerate(selenium_products[:5]):
                            print(f"    {i+1}. {text}: {link}")
                    
                    return {
                        'accessible': True,
                        'product_count': len(selenium_products),
                        'sample_products': selenium_products[:5]
                    }
                
            except Exception as selenium_error:
                print(f"  ❌ Lỗi Selenium: {selenium_error}")
        
        return {'accessible': False, 'error': str(e)}

def main():
    # Danh sách các website nhà thuốc phổ biến tại Việt Nam
    pharmacy_websites = [
        ("Pharmacity", "https://www.pharmacity.vn"),
        ("Long Châu", "https://nhathuoclongchau.com.vn"),
        ("Mediplus", "https://mediplus.vn"),
        ("Pharmart", "https://pharmart.vn"),
        ("Thuốc Tốt", "https://thuoctot.vn"),
        ("An Khang", "https://nhathuocankhang.com"),
        ("Phano", "https://phano.vn"),
        ("Medicare", "https://medicare.vn"),
        ("Nhà thuốc Thành Đạt", "https://nhathuocthanhdat.com"),
        ("Nhà thuốc FPT", "https://nhathuocfpt.com.vn")
    ]
    
    driver = init_browser()
    results = {}
    
    try:
        for name, url in pharmacy_websites:
            result = test_pharmacy_website(name, url, driver)
            results[name] = result
            time.sleep(2)  # Chờ giữa các request
        
        # Tổng kết
        print(f"\n{'='*80}")
        print(f"📊 TỔNG KẾT CÁC WEBSITE NHÀ THUỐC")
        print(f"{'='*80}")
        
        accessible_sites = []
        for name, result in results.items():
            if result.get('accessible', False):
                product_count = result.get('product_count', 0)
                accessible_sites.append((name, product_count))
                print(f"✅ {name}: {product_count} sản phẩm tiềm năng")
            else:
                error = result.get('error', 'Unknown error')
                print(f"❌ {name}: {error}")
        
        if accessible_sites:
            # Sắp xếp theo số lượng sản phẩm
            accessible_sites.sort(key=lambda x: x[1], reverse=True)
            
            print(f"\n🏆 TOP WEBSITE CÓ NHIỀU SẢN PHẨM NHẤT:")
            for i, (name, count) in enumerate(accessible_sites[:5]):
                print(f"  {i+1}. {name}: {count} sản phẩm")
            
            print(f"\n💡 KHUYẾN NGHỊ:")
            best_site = accessible_sites[0]
            print(f"🎯 Nên crawl từ {best_site[0]} vì có {best_site[1]} sản phẩm tiềm năng")
            
            if best_site[0] in results:
                sample_products = results[best_site[0]].get('sample_products', [])
                if sample_products:
                    print(f"\n📋 VÍ DỤ SẢN PHẨM TỪ {best_site[0]}:")
                    for i, (text, url) in enumerate(sample_products):
                        print(f"  {i+1}. {text}: {url}")
        else:
            print(f"\n❌ Không có website nào truy cập được!")
            print(f"💡 Có thể do:")
            print(f"   - Các website có chống bot")
            print(f"   - Cần VPN hoặc proxy")
            print(f"   - Cần thêm headers đặc biệt")
        
    except Exception as e:
        print(f"❌ Lỗi tổng quát: {e}")
    
    finally:
        driver.quit()
        print("🎉 Hoàn thành khảo sát!")

if __name__ == "__main__":
    main()
