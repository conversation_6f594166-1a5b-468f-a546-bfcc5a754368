import requests
from bs4 import BeautifulSoup
import re
import time
import csv

def crawl_product_detail(product_url):
    """Crawl chi tiết một sản phẩm"""
    print(f"🔍 Crawling: {product_url}")

    try:
        # Gửi request
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(product_url, headers=headers)
        response.raise_for_status()

        # Parse HTML
        soup = BeautifulSoup(response.content, 'html.parser')

        # Khởi tạo dữ liệu mặc định
        product_name = "Không có"
        product_code = "Không có"
        brand = "Không có"
        price = "Không có"
        category = "Không có"
        manufacturer = "Không có"
        registration_number = "Không có"
        product_classification = "Không có"  # Thay thế cho prescription_required
        active_ingredient = "Không có"
        indication = "Không có"
        target_user = "Không có"
        dosage_form = "Không có"
        specification = "Không có"
        usage = "Không có"
        product_images = "Không có"

        # Khởi tạo cột từ pmc-content-html
        tong_hop = "Không có"  # Chỉ dùng cho TH1
        pmc_columns = {}  # Dictionary để lưu các cột động từ TH2

        # Lấy tên sản phẩm
        title_selectors = ['h1', '.product-title', '.product-name']
        for selector in title_selectors:
            title_element = soup.select_one(selector)
            if title_element:
                product_name = title_element.text.strip()
                break

        # Lấy mã sản phẩm
        page_text = soup.get_text()
        code_match = re.search(r'P\d{5}', page_text)
        if code_match:
            product_code = code_match.group(0)

        # Lấy thương hiệu
        brand_link = soup.find('a', href=lambda x: x and '/thuong-hieu/' in x)
        if brand_link:
            brand = brand_link.text.strip().replace("Thương hiệu: ", "")

        # Lấy giá và đơn vị
        unit = "Không có"

        # Tìm giá có đơn vị cụ thể (ví dụ: "86.000 ₫/Kit")
        price_with_unit_patterns = [
            r'(\d+[.,]?\d*)\s*₫/(Kit|Hộp|Chai|Lọ|Vỉ|Gói|Bộ|Tuýp)',
            r'(\d+[.,]?\d*)\xa0₫/(Kit|Hộp|Chai|Lọ|Vỉ|Gói|Bộ|Tuýp)',
            r'(\d+[.,]?\d*)[\s\xa0]*₫/(Kit|Hộp|Chai|Lọ|Vỉ|Gói|Bộ|Tuýp)'
        ]

        found_price_unit = False
        for pattern in price_with_unit_patterns:
            price_with_unit_match = re.search(pattern, page_text)
            if price_with_unit_match:
                price = price_with_unit_match.group(1) + " ₫"
                unit = price_with_unit_match.group(2)
                found_price_unit = True
                break

        if not found_price_unit:
            # Tìm giá thông thường
            price_match = re.search(r'(\d+\.?\d*)\s*₫', page_text)
            if price_match:
                price = price_match.group(0)
                unit = "Không có"

        # Lấy số đăng ký
        reg_patterns = [
            r'Số đăng ký:\s*(\d+)',
            r'(\d{12})',
        ]
        for pattern in reg_patterns:
            reg_match = re.search(pattern, page_text)
            if reg_match:
                registration_number = reg_match.group(1) if reg_match.group(1).isdigit() else reg_match.group(0)
                break

        # Tìm danh mục
        if "Dầu, Cao xoa bóp" in page_text:
            category = "Dầu, Cao xoa bóp"
        elif "Thuốc không kê đơn" in page_text:
            category = "Thuốc không kê đơn"

        # Tìm nhà sản xuất
        manufacturer_patterns = [
            r'Nhà sản xuất([^A-Z]*?)Hoạt chất',
            r'Nhà sản xuất([^A-Z]*?)(?=Hoạt|Chỉ|Dạng|Quy)',
            r'Nhà sản xuất(.*?)(?=Hoạt chất|Chỉ định)'
        ]
        for pattern in manufacturer_patterns:
            match = re.search(pattern, page_text)
            if match:
                manufacturer = match.group(1).strip()
                break

        # Tìm hoạt chất
        active_patterns = [
            r'Hoạt chất([^A-Z]*?)Chỉ định',
            r'Hoạt chất([^A-Z]*?)(?=Chỉ|Dạng|Quy)',
            r'Hoạt chất(.*?)(?=Chỉ định|Dạng bào chế)'
        ]
        for pattern in active_patterns:
            match = re.search(pattern, page_text)
            if match:
                active_ingredient = match.group(1).strip()
                break

        # Tìm chỉ định
        indication_patterns = [
            r'Chỉ định([^A-Z]*?)Dạng bào chế',
            r'Chỉ định([^A-Z]*?)(?=Dạng|Quy)',
            r'Chỉ định(.*?)(?=Dạng bào chế|Quy cách)'
        ]
        for pattern in indication_patterns:
            match = re.search(pattern, page_text)
            if match:
                indication = match.group(1).strip()
                break

        # Tìm dạng bào chế
        dosage_patterns = [
            r'Dạng bào chế([^A-Z]*?)Quy cách',
            r'Dạng bào chế([^A-Z]*?)(?=Quy)',
            r'Dạng bào chế(.*?)(?=Quy cách|Lưu ý)'
        ]
        for pattern in dosage_patterns:
            match = re.search(pattern, page_text)
            if match:
                dosage_form = match.group(1).strip()
                break

        # Tìm quy cách
        spec_patterns = [
            r'Quy cách([^A-Z]*?)Lưu ý',
            r'Quy cách([^A-Z]*?)(?=Lưu|Đủ)',
            r'Quy cách(.*?)(?=Lưu ý|Đủ thuốc)'
        ]
        for pattern in spec_patterns:
            match = re.search(pattern, page_text)
            if match:
                specification = match.group(1).strip()
                break

        # Lấy phân loại sản phẩm từ button
        # Tìm trong tất cả button
        all_buttons = soup.find_all('button')
        classification_keywords = ['Kit', 'Hộp', 'Chai', 'Lọ', 'Vỉ', 'Gói', 'Bộ', 'Tuýp', 'Viên', 'Túi']

        for button in all_buttons:
            button_text = button.get_text(strip=True)
            for keyword in classification_keywords:
                if keyword.lower() in button_text.lower():
                    product_classification = keyword
                    break
            if product_classification != "Không có":
                break

        # Nếu không tìm thấy trong button, thử tìm trong text tổng thể
        if product_classification == "Không có":
            for keyword in classification_keywords:
                if keyword in page_text:
                    product_classification = keyword
                    break

        # Đối tượng sử dụng
        if "Trẻ nhỏ dưới" in page_text:
            target_user = "Người lớn và trẻ em trên 5 tuổi"
        elif "Phụ nữ có thai" in page_text and "chống chỉ định" in page_text.lower():
            target_user = "Không dành cho phụ nữ có thai"
        else:
            target_user = "Người lớn"

        # Tìm cách dùng
        usage_patterns = [
            r'Bôi.*?ngày\s*\d+\s*-\s*\d+\s*lần',
            r'\d+\s*viên.*?ngày',
            r'Liều dùng.*?ngày.*?lần',
            r'Cách dùng.*?ngày.*?lần'
        ]

        for pattern in usage_patterns:
            usage_match = re.search(pattern, page_text, re.IGNORECASE)
            if usage_match:
                usage = usage_match.group(0)
                break

        # Tìm lưu ý - tìm thẻ p có text "Lưu ý" và lấy div ngay dưới nó
        notes = "Không có"

        # Tìm thẻ p chứa text "Lưu ý"
        luu_y_elements = soup.find_all('p', string=lambda text: text and 'Lưu ý' in text)

        for luu_y_p in luu_y_elements:
            # Tìm div ngay sau thẻ p này
            next_div = luu_y_p.find_next_sibling('div')
            if next_div:
                notes_text = next_div.get_text(strip=True)
                if notes_text and len(notes_text) > 10:  # Đảm bảo có nội dung thực sự
                    # Làm sạch text và giới hạn độ dài
                    notes_text = re.sub(r'\s+', ' ', notes_text)  # Thay nhiều khoảng trắng bằng 1
                    if len(notes_text) > 500:  # Giới hạn 500 ký tự
                        notes_text = notes_text[:500] + "..."
                    notes = notes_text
                    break

        # Nếu không tìm thấy bằng cách trên, thử tìm bằng class hoặc cấu trúc khác
        if notes == "Không có":
            # Tìm theo cấu trúc grid với text "Lưu ý"
            grid_elements = soup.find_all('div', class_=lambda x: x and 'grid' in x)
            for grid in grid_elements:
                if 'Lưu ý' in grid.get_text():
                    # Tìm div chứa nội dung lưu ý trong grid này
                    content_divs = grid.find_all('div')
                    for div in content_divs:
                        div_text = div.get_text(strip=True)
                        if div_text and len(div_text) > 20 and 'Lưu ý' not in div_text:
                            notes_text = re.sub(r'\s+', ' ', div_text)
                            if len(notes_text) > 500:
                                notes_text = notes_text[:500] + "..."
                            notes = notes_text
                            break
                    if notes != "Không có":
                        break

        # Xử lý thẻ pmc-content-html để lấy cột động
        pmc_elements = soup.find_all(class_="pmc-content-html")

        if len(pmc_elements) == 1:
            # TH1: Chỉ có 1 thẻ pmc-content-html → lấy toàn bộ vào cột "Tổng hợp"
            tong_hop = pmc_elements[0].get_text(strip=True)
            if len(tong_hop) > 1000:  # Giới hạn độ dài
                tong_hop = tong_hop[:1000] + "..."

        elif len(pmc_elements) > 1:
            # TH2: Có nhiều thẻ → lấy thẻ h(x) làm tên cột, text trừ h(x) làm nội dung
            for element in pmc_elements:
                # Tìm thẻ h(x) trong element này
                h_tags = element.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])

                if h_tags:
                    # Lấy text của thẻ h(x) đầu tiên làm tên cột
                    column_name = h_tags[0].get_text(strip=True)

                    # Lấy toàn bộ text của element
                    full_text = element.get_text(strip=True)

                    # Loại bỏ text của tất cả thẻ h(x) khỏi nội dung
                    content_text = full_text
                    for h_tag in h_tags:
                        h_text = h_tag.get_text(strip=True)
                        content_text = content_text.replace(h_text, "", 1)  # Chỉ replace lần đầu tiên

                    # Làm sạch text
                    content_text = content_text.strip()
                    content_text = re.sub(r'\s+', ' ', content_text)  # Thay nhiều khoảng trắng bằng 1

                    if len(content_text) > 1000:  # Giới hạn độ dài
                        content_text = content_text[:1000] + "..."

                    # Lưu vào dictionary với tên cột làm key
                    if column_name and content_text:
                        pmc_columns[column_name] = content_text

        # Lấy ảnh sản phẩm (tối đa 5 ảnh)
        image_urls = []

        # Ưu tiên ảnh có mã sản phẩm trước
        priority_images = []
        other_images = []

        # Tìm tất cả ảnh
        all_images = soup.find_all('img')

        for img in all_images:
            src = img.get('src') or img.get('data-src')
            if src:
                # Chuyển đổi URL tương đối thành URL tuyệt đối
                if src.startswith('//'):
                    src = 'https:' + src
                elif src.startswith('/'):
                    src = 'https://www.pharmacity.vn' + src

                # Bỏ qua ảnh không liên quan
                skip_keywords = ['banner', 'badge', 'icon', 'logo', 'frame', 'button']
                if any(keyword in src.lower() for keyword in skip_keywords):
                    continue

                # Ưu tiên ảnh có mã sản phẩm
                if product_code != "Không có" and product_code.lower() in src.lower():
                    if src not in priority_images:
                        priority_images.append(src)
                # Ảnh ecommerce khác
                elif 'ecommerce' in src.lower() and src not in other_images:
                    other_images.append(src)

        # Gộp ảnh theo thứ tự ưu tiên
        image_urls = priority_images + other_images

        # Giới hạn tối đa 5 ảnh
        image_urls = image_urls[:5]

        # Chuyển đổi danh sách ảnh thành chuỗi, phân cách bằng dấu xuống dòng
        if image_urls:
            product_images = "\n".join(image_urls)
        else:
            product_images = "Không có"

        print(f"✅ Thành công: {product_name}")
        print(f"📷 Tìm thấy {len(image_urls)} ảnh")
        print(f"📋 Tìm thấy {len(pmc_columns)} cột PMC động")

        # Gom dữ liệu cơ bản (18 cột đầu)
        base_data = [
            product_url, product_name, product_code, brand, price, unit, category,
            manufacturer, registration_number, product_classification,
            active_ingredient, indication, target_user, dosage_form,
            specification, usage, notes, product_images
        ]

        # Thêm các cột động từ pmc-content-html (theo thứ tự alphabet)
        sorted_pmc_columns = sorted(pmc_columns.items())
        for column_name, content in sorted_pmc_columns:
            base_data.append(content)

        # Thêm cột "Tổng hợp" ở cuối (chỉ cho TH1)
        base_data.append(tong_hop)

        return base_data, sorted_pmc_columns

    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return None

def main():
    # Danh sách URL test
    test_urls = [
        "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html",
        "https://www.pharmacity.vn/genesign-test-respiratory-combo-antigen-rapid-20-test-hop.html"
    ]

    print(f"🚀 Bắt đầu crawl {len(test_urls)} sản phẩm test...")

    # Headers cơ bản (18 cột đầu)
    base_headers = ["URL", "Tên sản phẩm", "Mã sản phẩm", "Thương hiệu", "Giá", "Đơn vị", "Danh mục", "Nhà sản xuất", "Số đăng ký", "Phân loại sản phẩm", "Hoạt chất", "Chỉ định", "Đối tượng sử dụng", "Dạng bào chế", "Quy cách", "Cách dùng", "Lưu ý", "Ảnh sản phẩm"]

    all_data = []
    all_pmc_columns = set()  # Tập hợp tất cả tên cột PMC

    for i, url in enumerate(test_urls):
        print(f"\n[{i+1}/{len(test_urls)}] ", end="")

        result = crawl_product_detail(url)
        if result:
            product_data, pmc_columns = result
            all_data.append(product_data)

            # Thu thập tất cả tên cột PMC
            for column_name, _ in pmc_columns:
                all_pmc_columns.add(column_name)

        # Nghỉ 2 giây giữa các request
        time.sleep(2)

    # Tạo headers đầy đủ
    sorted_pmc_headers = sorted(list(all_pmc_columns))
    full_headers = base_headers + sorted_pmc_headers + ["Tổng hợp"]

    print(f"\n📊 Tổng cộng có {len(full_headers)} cột:")
    print(f"   - 18 cột cơ bản")
    print(f"   - {len(sorted_pmc_headers)} cột PMC động: {sorted_pmc_headers}")
    print(f"   - 1 cột Tổng hợp")

    # Ghi vào file CSV
    if all_data:
        print(f"\n📊 Ghi {len(all_data)} sản phẩm vào file CSV...")
        try:
            with open('pharmacity_products.csv', 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(full_headers)
                writer.writerows(all_data)
            print(f"✅ Đã ghi thành công vào pharmacity_products.csv!")
        except Exception as e:
            print(f"❌ Lỗi khi ghi CSV: {str(e)}")

    # In kết quả
    print(f"\n📋 Kết quả crawl {len(all_data)} sản phẩm:")
    for i, data in enumerate(all_data):
        print(f"\n{i+1}. {data[1]} ({data[2]})")
        print(f"   Thương hiệu: {data[3]}")
        print(f"   Giá: {data[4]}")
        print(f"   Đơn vị: {data[5]}")
        print(f"   Danh mục: {data[6]}")
        print(f"   Nhà sản xuất: {data[7]}")
        print(f"   Số đăng ký: {data[8]}")

    print("🎉 Hoàn thành!")

if __name__ == "__main__":
    main()
