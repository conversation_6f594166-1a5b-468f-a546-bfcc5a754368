import requests
from bs4 import BeautifulSoup
import re

def test_th2_logic(url):
    """Test logic TH2 mới - lấy ID thẻ cha làm tên cột"""
    print(f"🧪 Test logic TH2 mới: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        product_soup = BeautifulSoup(response.content, 'html.parser')
        
        # Tìm tất cả thẻ pmc-content-html
        pmc_elements = product_soup.find_all(class_="pmc-content-html")
        print(f"🔍 Tìm thấy {len(pmc_elements)} thẻ pmc-content-html")
        
        if len(pmc_elements) > 1:
            print(f"✅ Đây là TH2 - có {len(pmc_elements)} thẻ pmc-content-html")
            pmc_columns = {}
            
            for i, element in enumerate(pmc_elements):
                print(f"\n--- Thẻ pmc-content-html {i+1} ---")
                
                # Tìm thẻ cha có ID của element pmc-content-html này
                parent_element = element.parent
                column_name = None
                
                print(f"🔍 Tìm ID từ thẻ cha...")
                
                # Tìm ID từ thẻ cha hoặc các thẻ cha phía trên
                current_element = parent_element
                level = 1
                while current_element and column_name is None and level <= 5:  # Giới hạn 5 cấp
                    element_id = current_element.get('id')
                    element_tag = current_element.name
                    print(f"   Cấp {level}: <{element_tag}> id='{element_id}'")
                    
                    if element_id:
                        column_name = element_id
                        print(f"   ✅ Tìm thấy ID: '{column_name}' ở cấp {level}")
                        break
                    current_element = current_element.parent
                    level += 1
                
                # Nếu không tìm thấy ID, thử lấy từ thẻ h(x) như cũ (fallback)
                if not column_name:
                    print(f"   ❌ Không tìm thấy ID, fallback sang thẻ h(x)...")
                    h_tags = element.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                    if h_tags:
                        column_name = h_tags[0].get_text(strip=True)
                        print(f"   ✅ Fallback: Dùng text h(x): '{column_name}'")
                
                if column_name:
                    # Lấy toàn bộ text của element
                    full_text = element.get_text(strip=True)
                    print(f"📝 Text đầy đủ: {repr(full_text[:100])}...")
                    
                    # Loại bỏ text của tất cả thẻ h(x) khỏi nội dung
                    content_text = full_text
                    h_tags = element.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                    
                    print(f"🔍 Tìm thấy {len(h_tags)} thẻ h(x) để loại bỏ:")
                    for j, h_tag in enumerate(h_tags):
                        h_text = h_tag.get_text(strip=True)
                        print(f"   h{j+1}: {repr(h_text)}")
                        content_text = content_text.replace(h_text, "", 1)  # Chỉ replace lần đầu tiên
                    
                    # Làm sạch text
                    content_text = content_text.strip()
                    content_text = re.sub(r'\s+', ' ', content_text)  # Thay nhiều khoảng trắng bằng 1
                    
                    print(f"📄 Nội dung sau khi loại bỏ h(x): {repr(content_text[:100])}...")
                    
                    if len(content_text) > 1000:  # Giới hạn độ dài
                        content_text = content_text[:1000] + "..."
                    
                    # Lưu vào dictionary với ID thẻ cha làm key
                    if column_name and content_text:
                        pmc_columns[column_name] = content_text
                        print(f"✅ Đã lưu cột: '{column_name}' = '{content_text[:50]}...'")
                else:
                    print(f"❌ Không tìm thấy tên cột cho thẻ {i+1}")
            
            print(f"\n🎯 Kết quả cuối cùng:")
            print(f"   Số cột động tạo được: {len(pmc_columns)}")
            for col_name, content in pmc_columns.items():
                print(f"   📋 '{col_name}': {content[:50]}...")
            
            return pmc_columns
            
        elif len(pmc_elements) == 1:
            print(f"ℹ️  Đây là TH1 - chỉ có 1 thẻ pmc-content-html")
            return {"Tổng hợp": pmc_elements[0].get_text(strip=True)[:100] + "..."}
        else:
            print(f"❌ Không tìm thấy thẻ pmc-content-html nào")
            return {}
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return {}

if __name__ == "__main__":
    # Test với sản phẩm có nhiều thẻ pmc-content-html
    test_urls = [
        "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html",
        "https://www.pharmacity.vn/genesign-test-respiratory-combo-antigen-rapid-20-test-hop.html",
    ]
    
    for i, test_url in enumerate(test_urls):
        print(f"\n{'='*80}")
        print(f"TEST {i+1}")
        result = test_th2_logic(test_url)
        
        if result:
            print(f"\n✅ Test thành công!")
            print(f"   📊 Số cột tạo được: {len(result)}")
        else:
            print(f"❌ Test thất bại!")
