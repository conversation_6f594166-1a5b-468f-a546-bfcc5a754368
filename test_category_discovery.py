from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time

def init_browser():
    """Khởi tạo browser"""
    print("🚀 Khởi tạo Chrome browser...")
    service = Service(ChromeDriverManager().install())
    
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--log-level=3")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.maximize_window()
    
    return driver

def discover_all_categories(driver):
    """Tự động khám phá tất cả categories từ trang chủ thuốc Pharmacity"""
    print("\n🔍 KHÁM PHÁ TẤT CẢ CATEGORIES TỪ TRANG CHỦ THUỐC")
    print("="*60)
    
    # Bắt đầu từ trang chủ thuốc
    main_drug_url = "https://www.pharmacity.vn/duoc-pham"
    print(f"📄 Vào trang chủ thuốc: {main_drug_url}")
    driver.get(main_drug_url)
    time.sleep(5)
    
    all_categories = []
    
    # Lấy HTML của trang
    soup = BeautifulSoup(driver.page_source, "html.parser")
    
    # Tìm tất cả các category cha (4 cái chính)
    print("\n🔍 Bước 1: Tìm các category cha...")
    
    main_categories = []
    
    # Tìm links có pattern thuốc
    all_links = soup.find_all('a', href=True)
    for link in all_links:
        href = link.get('href')
        text = link.get_text(strip=True)
        
        if href and text:
            # Kiểm tra pattern cho category thuốc chính
            drug_patterns = [
                'thuoc-khong-ke-don',
                'thuoc-ke-don', 
                'thuoc-khac',
                'vitamin-thuc-pham-chuc-nang'
            ]
            
            if any(pattern in href for pattern in drug_patterns):
                if href.startswith('/'):
                    full_url = 'https://www.pharmacity.vn' + href
                elif href.startswith('https://www.pharmacity.vn'):
                    full_url = href
                else:
                    continue
                
                # Loại bỏ query parameters
                if '?' in full_url:
                    full_url = full_url.split('?')[0]
                
                if full_url not in [cat[1] for cat in main_categories]:
                    main_categories.append((text, full_url))
                    print(f"  ✅ Tìm thấy category cha: {text} → {full_url}")
    
    print(f"\n📊 Tìm thấy {len(main_categories)} category cha")
    
    # Bước 2: Với mỗi category cha, tìm subcategories
    print(f"\n🔍 Bước 2: Tìm subcategories cho từng category cha...")
    
    for main_name, main_url in main_categories:
        print(f"\n🏷️ Phân tích category: {main_name}")
        print(f"📄 URL: {main_url}")
        
        try:
            driver.get(main_url)
            time.sleep(3)
            
            sub_soup = BeautifulSoup(driver.page_source, "html.parser")
            
            # Tìm subcategories bằng nhiều cách
            subcategories = []
            
            # Cách 1: Tìm trong các thẻ có class chứa category/grid
            category_grids = sub_soup.find_all(['div', 'section'], class_=lambda x: x and any(keyword in x.lower() for keyword in ['grid', 'category', 'product-category']))
            
            for grid in category_grids:
                grid_links = grid.find_all('a', href=True)
                for link in grid_links:
                    href = link.get('href')
                    text = link.get_text(strip=True)
                    
                    if href and text and len(text) > 3:
                        if href.startswith('/'):
                            full_url = 'https://www.pharmacity.vn' + href
                        elif href.startswith('https://www.pharmacity.vn'):
                            full_url = href
                        else:
                            continue
                        
                        # Loại bỏ query parameters
                        if '?' in full_url:
                            full_url = full_url.split('?')[0]
                        
                        # Kiểm tra xem có phải subcategory thuốc không
                        if (any(keyword in full_url for keyword in ['thuoc-', 'vitamin-', 'giam-dau']) and
                            full_url != main_url and
                            full_url not in [sub[1] for sub in subcategories] and
                            'pharmacity.vn' in full_url and
                            not any(skip in full_url for skip in ['search', 'cart', 'account', 'login'])):
                            
                            subcategories.append((text, full_url))
            
            # Cách 2: Tìm tất cả links có pattern thuốc
            all_sub_links = sub_soup.find_all('a', href=True)
            for link in all_sub_links:
                href = link.get('href')
                text = link.get_text(strip=True)

                if href and text and len(text) > 3:
                    if href.startswith('/'):
                        full_url = 'https://www.pharmacity.vn' + href
                    elif href.startswith('https://www.pharmacity.vn'):
                        full_url = href
                    else:
                        continue

                    # Loại bỏ query parameters
                    if '?' in full_url:
                        full_url = full_url.split('?')[0]

                    # Kiểm tra pattern subcategory cụ thể hơn
                    subcategory_patterns = [
                        'thuoc-ngua-thai', 'thuoc-khang-di-ung', 'thuoc-khang-viem', 'thuoc-cam-lanh',
                        'thuoc-giam-can', 'thuoc-mattaimui', 'thuoc-tieu-hoa', 'thuoc-danh-cho-nam',
                        'giam-dau-ha-sot', 'thuoc-da-lieu', 'thuoc-danh-cho-phu-nu', 'thuoc-than-kinh',
                        'thuoc-co-xuong-khop', 'dau-gio-dau-cu-la', 'vitamin-va-khoang-chat'
                    ]
                    
                    if (any(pattern in full_url for pattern in subcategory_patterns) and
                        full_url != main_url and
                        full_url not in [sub[1] for sub in subcategories] and
                        'pharmacity.vn' in full_url):

                        subcategories.append((text, full_url))
            
            print(f"  📊 Tìm thấy {len(subcategories)} subcategories:")
            for i, (sub_name, sub_url) in enumerate(subcategories[:15]):  # Hiển thị 15 đầu
                print(f"    {i+1}. {sub_name}")
            
            if len(subcategories) > 15:
                print(f"    ... và {len(subcategories) - 15} subcategories khác")
            
            # Thêm category cha vào danh sách
            all_categories.append((main_name, main_url))
            
            # Thêm tất cả subcategories
            all_categories.extend(subcategories)
            
        except Exception as e:
            print(f"  ❌ Lỗi khi phân tích {main_name}: {e}")
            # Vẫn thêm category cha nếu không lấy được subcategories
            all_categories.append((main_name, main_url))
    
    print(f"\n✅ HOÀN THÀNH KHÁM PHÁ CATEGORIES")
    print(f"📊 Tổng cộng: {len(all_categories)} categories")
    print("="*60)
    
    return [url for name, url in all_categories]

def main():
    driver = init_browser()
    
    try:
        categories = discover_all_categories(driver)
        
        print(f"\n{'='*80}")
        print(f"🎯 KẾT QUẢ CUỐI CÙNG")
        print(f"{'='*80}")
        print(f"📊 Tổng số categories khám phá được: {len(categories)}")
        print(f"\n📋 DANH SÁCH TẤT CẢ CATEGORIES:")
        for i, url in enumerate(categories):
            print(f"  {i+1}. {url}")
        print(f"{'='*80}")
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
