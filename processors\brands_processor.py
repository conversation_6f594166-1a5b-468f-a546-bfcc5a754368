# ===== BRANDS PROCESSOR =====

from processors.base_processor import BaseProcessor
from utils.image_downloader import download_and_get_db_path

class BrandsProcessor(BaseProcessor):
    """Processor cho bảng brands"""
    
    def process_row(self, row, attribute_option_id=None):
        """Xử lý dữ liệu cho bảng brands"""
        # Lấy dữ liệu từ sheet Brands
        image_url = self.get_column_value(row, 0)      # Cột A - Image URL
        brand_name = self.get_column_value(row, 1)     # Cột B - Brand Name
        
        # Kiểm tra dữ liệu hợp lệ
        if not self.is_valid_brand_name(brand_name):
            return None
        
        # Download ảnh và lấy path cho DB
        brand_image_path = None
        if image_url:
            brand_image_path = download_and_get_db_path(image_url)
        
        return {
            'attribute_option_id': attribute_option_id,  # ID từ bảng attribute_options
            'brand_name': brand_name,
            'brand_image': brand_image_path,  # Path ảnh đã download
            'brand_description': "",          # Mặc định rỗng
            'outstanding_product_id': None    # Mặc định NULL
        }

class AttributeOptionsProcessor(BaseProcessor):
    """Processor cho bảng attribute_options"""
    
    def process_row(self, row, **kwargs):
        """Xử lý dữ liệu cho bảng attribute_options"""
        # Lấy dữ liệu từ sheet Brands
        admin_name = self.get_column_value(row, 1)   # Cột B - Brand Name
        
        # Kiểm tra dữ liệu hợp lệ
        if not self.is_valid_brand_name(admin_name):
            return None
        
        return {
            'attribute_id': 43,      # Mặc định 43
            'admin_name': admin_name,
            'sort_order': 1,         # Mặc định 1
            'swatch_value': None,    # Mặc định NULL
            'is_featured': 0,        # Mặc định 0
            'brand_image': None,     # Mặc định NULL
            'brand_description': None # Mặc định NULL
        }

class ProductsProcessor(BaseProcessor):
    """Processor cho bảng products (ví dụ mở rộng)"""
    
    def process_row(self, row, **kwargs):
        """Xử lý dữ liệu cho bảng products"""
        # Lấy dữ liệu từ mapping
        product_code = self.get_column_value(row, self.mapping.get('product_code', 2))
        product_name = self.get_column_value(row, self.mapping.get('product_name', 1))
        brand = self.get_column_value(row, self.mapping.get('brand', 3))
        price = self.get_column_value(row, self.mapping.get('price', 4))
        category = self.get_column_value(row, self.mapping.get('category', 6))
        
        # Kiểm tra dữ liệu hợp lệ
        if not product_name or not product_code:
            return None
        
        return {
            'product_code': product_code,
            'product_name': product_name,
            'brand': brand,
            'price': price,
            'category': category
        }
