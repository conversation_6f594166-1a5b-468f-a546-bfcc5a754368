import requests
from bs4 import BeautifulSoup
import re

def debug_pmc_content(url):
    """Debug thẻ pmc-content-html"""
    print(f"🔍 Debug pmc-content-html từ: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Tìm tất cả thẻ có class "pmc-content-html"
        pmc_elements = soup.find_all(class_="pmc-content-html")
        print(f"📄 Tìm thấy {len(pmc_elements)} thẻ có class 'pmc-content-html'")
        
        if len(pmc_elements) == 0:
            print("❌ Không tìm thấy thẻ pmc-content-html nào")
            return
        
        elif len(pmc_elements) == 1:
            print("\n📋 TH1: Chỉ có 1 thẻ pmc-content-html")
            element = pmc_elements[0]
            text_content = element.get_text(strip=True)
            print(f"✅ Nội dung tổng hợp: {text_content[:200]}...")
            return {"Tổng hợp": text_content}
        
        else:
            print(f"\n📋 TH2: Có {len(pmc_elements)} thẻ pmc-content-html")
            result = {}
            
            for i, element in enumerate(pmc_elements):
                print(f"\n--- Thẻ thứ {i+1} ---")
                
                # Tìm thẻ cha có ID
                parent = element.parent
                parent_id = None
                
                # Tìm thẻ cha có ID (có thể phải đi lên nhiều cấp)
                current = element
                while current and current.parent:
                    current = current.parent
                    if current.get('id'):
                        parent_id = current.get('id')
                        break
                
                if parent_id:
                    print(f"🏷️  ID thẻ cha: {parent_id}")
                    
                    # Lấy text content
                    text_content = element.get_text(strip=True)
                    print(f"📝 Nội dung: {text_content[:100]}...")
                    
                    # Map ID sang tên cột tiếng Việt
                    column_mapping = {
                        'thanh-phan': 'Thành phần',
                        'chi-dinh': 'Chỉ định', 
                        'cach-su-dung': 'Cách sử dụng',
                        'than-trong': 'Thận trọng',
                        'thong-tin-san-xuat': 'Thông tin sản xuất',
                        'huong-dan-su-dung': 'Cách sử dụng',
                        'lieu-dung': 'Cách sử dụng',
                        'cach-dung': 'Cách sử dụng'
                    }
                    
                    column_name = column_mapping.get(parent_id, parent_id)
                    result[column_name] = text_content
                    print(f"✅ Cột: {column_name}")
                    
                else:
                    print(f"❌ Không tìm thấy ID thẻ cha")
                    print(f"🔍 Cấu trúc thẻ cha: {element.parent.name if element.parent else 'None'}")
                    if element.parent:
                        print(f"    Class: {element.parent.get('class', [])}")
                        print(f"    Attrs: {dict(element.parent.attrs)}")
            
            return result
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return None

if __name__ == "__main__":
    # Test với nhiều sản phẩm khác nhau
    test_urls = [
        "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html",
        "https://www.pharmacity.vn/genesign-test-respiratory-combo-antigen-rapid-20-test-hop.html"
    ]
    
    for i, test_url in enumerate(test_urls):
        print(f"\n{'='*80}")
        print(f"TEST {i+1}")
        result = debug_pmc_content(test_url)
        
        if result:
            print(f"\n📊 Kết quả:")
            for column, content in result.items():
                print(f"   {column}: {content[:100]}...")
        else:
            print(f"\n❌ Không lấy được dữ liệu!")
