# ===== GOOGLE SHEETS SERVICE =====

from config.google_sheets import get_google_sheets_client, get_worksheet, get_sheet_data

class SheetsService:
    """Service để quản lý Google Sheets operations"""
    
    def __init__(self):
        self.client = None
    
    def connect(self):
        """Kết nối Google Sheets"""
        print("🔗 Đang kết nối Google Sheets...")
        self.client = get_google_sheets_client()
        return self.client
    
    def get_data(self, sheet_name, start_row=2, max_rows=None):
        """L<PERSON>y dữ liệu từ Google Sheets"""
        if not self.client:
            raise Exception("Chưa kết nối Google Sheets. Hãy gọi connect() trước.")
        
        worksheet = get_worksheet(self.client, sheet_name)
        return get_sheet_data(worksheet, start_row, max_rows)
    
    def get_client(self):
        """Lấy client hiện tại"""
        if not self.client:
            raise Exception("Chưa kết nối Google Sheets. <PERSON><PERSON><PERSON> gọi connect() trước.")
        return self.client
