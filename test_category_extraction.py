import requests
from bs4 import BeautifulSoup
import re

def test_category_extraction(url):
    """Test logic lấy danh mục mới"""
    print(f"🧪 Test lấy danh mục: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        product_soup = BeautifulSoup(response.content, 'html.parser')
        category = "Không có"
        
        # Logic mới từ crawl_pharmacity.py
        # Phương pháp 1: Tìm thẻ p có text "Danh mục" và lấy div ngay dưới nó
        print("🔍 Phương pháp 1: Tìm thẻ p có text 'Danh mục'...")
        danh_muc_elements = product_soup.find_all('p', string=lambda text: text and 'Danh mục' in text)
        print(f"   Tìm thấy {len(danh_muc_elements)} thẻ p có text 'Danh mục'")
        
        for i, danh_muc_p in enumerate(danh_muc_elements):
            print(f"   Thẻ p {i+1}: {repr(danh_muc_p.get_text())}")
            # Tìm div ngay sau thẻ p này
            next_div = danh_muc_p.find_next_sibling('div')
            if next_div:
                category_text = next_div.get_text(strip=True)
                print(f"   Div ngay sau: {repr(category_text[:100])}")
                if category_text and len(category_text) > 3:  # Đảm bảo có nội dung thực sự
                    # Làm sạch text
                    category_text = re.sub(r'\s+', ' ', category_text)  # Thay nhiều khoảng trắng bằng 1
                    category = category_text
                    print(f"   ✅ Tìm thấy danh mục: {category}")
                    break
            else:
                print(f"   ❌ Không tìm thấy div ngay sau thẻ p {i+1}")
        
        # Phương pháp 2: Tìm theo cấu trúc grid với text "Danh mục"
        if category == "Không có":
            print("🔍 Phương pháp 2: Tìm trong grid...")
            grid_elements = product_soup.find_all('div', class_=lambda x: x and 'grid' in x)
            print(f"   Tìm thấy {len(grid_elements)} div có class chứa 'grid'")
            
            for i, grid in enumerate(grid_elements):
                if 'Danh mục' in grid.get_text():
                    print(f"   Grid {i+1} có chứa 'Danh mục'")
                    # Tìm div chứa nội dung danh mục trong grid này
                    content_divs = grid.find_all('div')
                    for j, div in enumerate(content_divs):
                        div_text = div.get_text(strip=True)
                        if div_text and len(div_text) > 5 and 'Danh mục' not in div_text:
                            # Kiểm tra nếu có vẻ như là danh mục (không phải số, không phải link)
                            if not div_text.isdigit() and 'http' not in div_text.lower():
                                print(f"     Div {j+1}: {repr(div_text[:50])}")
                                category_text = re.sub(r'\s+', ' ', div_text)
                                category = category_text
                                print(f"   ✅ Tìm thấy danh mục trong grid: {category}")
                                break
                    if category != "Không có":
                        break
        
        # Phương pháp 3: Fallback với patterns cũ
        if category == "Không có":
            print("🔍 Phương pháp 3: Fallback với patterns...")
            page_text = product_soup.get_text()
            if "Dầu, Cao xoa bóp" in page_text:
                category = "Dầu, Cao xoa bóp"
                print("   ✅ Tìm thấy: Dầu, Cao xoa bóp")
            elif "Thuốc không kê đơn" in page_text:
                category = "Thuốc không kê đơn"
                print("   ✅ Tìm thấy: Thuốc không kê đơn")
            else:
                # Tìm từ patterns
                category_patterns = [
                    r'Danh mục([^A-Z]*?)Nhà sản xuất',
                    r'Danh mục([^A-Z]*?)(?=Nhà|Hoạt|Chỉ|Dạng|Quy)',
                    r'Danh mục(.*?)(?=Nhà sản xuất|Hoạt chất)'
                ]
                for pattern in category_patterns:
                    match = re.search(pattern, page_text)
                    if match:
                        cat_text = match.group(1).strip()
                        # Lấy phần cuối cùng sau dấu "..."
                        if "..." in cat_text:
                            category = cat_text.split("...")[-1].strip()
                        else:
                            category = cat_text
                        print(f"   ✅ Tìm thấy bằng pattern: {category}")
                        break
        
        print(f"🎯 Kết quả cuối cùng: {category}")
        return category
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return "Không có"

if __name__ == "__main__":
    # Test với các sản phẩm khác nhau
    test_urls = [
        "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html",
        "https://www.pharmacity.vn/genesign-test-respiratory-combo-antigen-rapid-20-test-hop.html",
    ]
    
    for i, test_url in enumerate(test_urls):
        print(f"\n{'='*80}")
        print(f"TEST {i+1}")
        category = test_category_extraction(test_url)
        print(f"🎯 Danh mục cuối cùng: {category}")
