def get_column_letter(index):
    """Chuyển đổi index cột thành tên cột (A, B, ..., Z, AA, AB, ...)"""
    if index < 26:
        return chr(ord('A') + index)
    else:
        # AA, AB, AC, ... (index 26, 27, 28, ...)
        first_letter = chr(ord('A') + (index // 26) - 1)
        second_letter = chr(ord('A') + (index % 26))
        return first_letter + second_letter

def test_column_letters():
    """Test function chuyển đổi index thành tên cột"""
    print("🧪 TEST CHUYỂN ĐỔI INDEX THÀNH TÊN CỘT")
    print("="*50)
    
    # Test các index quan trọng
    test_cases = [
        (0, "A"),
        (1, "B"),
        (18, "S"),  # Cột "Tổng hợp"
        (19, "T"),  # Cột động đầu tiên
        (20, "U"),
        (25, "Z"),  # Cột cuối cùng của alphabet đầu tiên
        (26, "AA"), # Cột đầu tiên của alphabet thứ hai
        (27, "AB"),
        (28, "AC"),
        (51, "AZ"), # C<PERSON>t cuối cùng của AA-AZ
        (52, "BA"), # Cột đầu tiên của BA-BZ
    ]
    
    print("📋 Kiểm tra các index quan trọng:")
    for index, expected in test_cases:
        result = get_column_letter(index)
        status = "✅" if result == expected else "❌"
        print(f"   Index {index:2d} → {result:3s} (mong đợi: {expected:3s}) {status}")
    
    print("\n📋 Mô phỏng cấu trúc cột thực tế:")
    
    # Header cố định
    fixed_header = ["URL", "Tên sản phẩm", "Mã sản phẩm", "Thương hiệu", "Giá", "Đơn vị", "Danh mục", "Nhà sản xuất", "Số đăng ký", "Phân loại sản phẩm", "Hoạt chất", "Chỉ định", "Đối tượng sử dụng", "Dạng bào chế", "Quy cách", "Cách dùng", "Lưu ý", "Ảnh sản phẩm", "Tổng hợp"]
    
    print(f"   📊 Header cố định ({len(fixed_header)} cột):")
    for i, header in enumerate(fixed_header):
        col_letter = get_column_letter(i)
        print(f"      {col_letter}: {header}")
    
    # Cột động giả lập
    dynamic_cols = ["mo-ta", "thanh-phan", "chi-dinh", "huong-dan-su-dung", "than-trong", "thong-tin-san-xuat"]
    
    print(f"\n   📊 Cột động ({len(dynamic_cols)} cột):")
    for i, col_name in enumerate(dynamic_cols):
        col_index = len(fixed_header) + i  # 19 + i
        col_letter = get_column_letter(col_index)
        print(f"      {col_letter}: {col_name}")
    
    total_cols = len(fixed_header) + len(dynamic_cols)
    print(f"\n   📊 Tổng cộng: {total_cols} cột (A - {get_column_letter(total_cols-1)})")

if __name__ == "__main__":
    test_column_letters()
