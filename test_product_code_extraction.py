import requests
from bs4 import BeautifulSoup
import re

def test_product_code_extraction(url):
    """Test logic lấy mã sản phẩm mới"""
    print(f"🧪 Test lấy mã sản phẩm: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        product_soup = BeautifulSoup(response.content, 'html.parser')
        product_code = "Không có"
        
        # Logic mới từ crawl_pharmacity.py
        # Phương pháp 1: Tìm span chứa mã sản phẩm bắt đầu bằng "P" (ưu tiên)
        print("🔍 Phương pháp 1: Tìm trong tất cả span...")
        all_spans = product_soup.find_all('span')
        print(f"   T<PERSON><PERSON> thấy {len(all_spans)} span")
        
        for i, span in enumerate(all_spans):
            span_text = span.get_text(strip=True)
            # Tìm mã sản phẩm bắt đầu bằng P và có 5-6 chữ số
            if re.match(r'^P\d{5,6}$', span_text):
                product_code = span_text
                print(f"   ✅ Tìm thấy mã sản phẩm trong span {i+1}: {product_code}")
                break
            elif span_text.startswith('P') and len(span_text) > 3:
                print(f"   🔍 Span {i+1} có text bắt đầu bằng P: {repr(span_text)}")
        
        # Phương pháp 2: Nếu không tìm thấy, tìm trong text gần thương hiệu
        if product_code == "Không có":
            print("🔍 Phương pháp 2: Tìm gần thương hiệu...")
            # Tìm thẻ a chứa thương hiệu
            brand_link = product_soup.find('a', href=lambda x: x and '/thuong-hieu/' in x)
            if brand_link:
                print(f"   Tìm thấy link thương hiệu: {brand_link.get_text()}")
                # Tìm trong parent element của thương hiệu
                parent = brand_link.parent
                if parent:
                    parent_text = parent.get_text()
                    print(f"   Text parent: {repr(parent_text[:100])}")
                    code_match = re.search(r'P\d{5,6}', parent_text)
                    if code_match:
                        product_code = code_match.group()
                        print(f"   ✅ Tìm thấy mã sản phẩm trong parent: {product_code}")
            else:
                print("   ❌ Không tìm thấy link thương hiệu")
        
        # Phương pháp 3: Tìm trong toàn bộ text trang (fallback)
        if product_code == "Không có":
            print("🔍 Phương pháp 3: Tìm trong toàn bộ text...")
            page_text = product_soup.get_text()
            code_matches = re.findall(r'P\d{5,6}', page_text)
            if code_matches:
                product_code = code_matches[0]  # Lấy cái đầu tiên
                print(f"   ✅ Tìm thấy mã sản phẩm trong text: {product_code}")
                print(f"   📋 Tất cả mã tìm thấy: {code_matches}")
            else:
                print("   ❌ Không tìm thấy mã sản phẩm nào")
        
        print(f"🎯 Kết quả cuối cùng: {product_code}")
        return product_code
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return "Không có"

if __name__ == "__main__":
    # Test với các sản phẩm khác nhau
    test_urls = [
        "https://www.pharmacity.vn/vien-nang-bidiphar-khong-co.html",  # Sản phẩm có mã P17386
        "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html",  # Sản phẩm khác
        "https://www.pharmacity.vn/genesign-test-respiratory-combo-antigen-rapid-20-test-hop.html",  # Sản phẩm test
    ]
    
    for i, test_url in enumerate(test_urls):
        print(f"\n{'='*80}")
        print(f"TEST {i+1}")
        product_code = test_product_code_extraction(test_url)
        print(f"🎯 Mã sản phẩm cuối cùng: {product_code}")
