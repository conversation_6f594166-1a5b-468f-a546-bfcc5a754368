import pymysql
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import time
import sys
import os
import requests
import uuid
import random
from urllib.parse import urlparse

# ===== CẤU HÌNH =====
# Google Sheets config
SHEET_ID = "1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8"
SHEET_NAME = "MedicalDevice"  # Sheet MedicalDevice thay vì Medicine
CREDENTIALS_FILE = "medical-crawl-2024-013b6faaa588.json"

# MySQL config cho Docker Adminer
DB_CONFIG = {
    'host': 'localhost',     # Hoặc thử 'mysql' nếu localhost không được
    'port': 3307,            # Port Docker MySQL (thường là 3307)
    'user': 'sail',          # Username từ Adminer
    'password': 'password',  # Password từ Adminer
    'database': 'bagisto',   # Database từ Adminer
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# ===== CẤU HÌNH TEST =====
ROW_NUMBER = 1  # 🎯 KIỂM SOÁT SỐ HÀNG: Số dòng tối đa muốn xử lý (None = tất cả dòng)
START_ROW = 2  # Bắt đầu từ dòng nào (7 = bỏ qua header)

# Cấu hình ảnh cho product_images
LOCAL_IMAGE_PATH = r"C:\Working\Medical-EC\MEDICAL_EC_SYSTEM\storage\app\public\EC-Images"
IMAGE_URL_PREFIX = "EC-Images/"  # Prefix để lưu vào DB

# ===== MAPPING CỘT GOOGLE SHEETS CHO MEDICAL DEVICES =====
# Mapping theo thứ tự cột trong Google Sheets (loại bỏ các cột không cần)
COLUMN_MAPPING = {
    'url': 0,                    # Cột A - URL (không cần)
    'name': 1,                   # Cột B - Tên sản phẩm
    'sku': 2,                    # Cột C - Mã sản phẩm
    'brand': 3,                  # Cột D - Thương hiệu
    'price': 4,                  # Cột E - Giá
    'unit_type': 5,              # Cột F - Đơn vị
    'category': 6,               # Cột G - Danh mục
    'manufacturer': 7,           # Cột H - Nhà sản xuất
    # 'registration_number': 8,  # Cột I - Số đăng kí (LOẠI BỎ)
    'product_classification': 9, # Cột J - Phân loại sản phẩm
    # 'active_ingredient': 10,   # Cột K - Hoạt chất (LOẠI BỎ)
    'indication': 11,            # Cột L - Chỉ định
    # 'target_user': 12,         # Cột M - Đối tượng sử dụng (LOẠI BỎ)
    'dosage_form': 13,           # Cột N - Dạng bào chế
    'packing': 14,               # Cột O - Quy cách
    # 'usage': 15,               # Cột P - Cách dùng (LOẠI BỎ)
    'note': 16,                  # Cột Q - Lưu ý
    'product_image': 17,         # Cột R - Ảnh sản phẩm
    'summary': 18,               # Cột S - Tổng hợp (xử lý riêng)
    'description': 19,           # Cột T - Mô tả
    'ingredient': 20,            # Cột U - Thành phần
    'indication_2': 21,          # Cột V - Chỉ định (không lưu - trùng)
    'usage_instructions': 22,    # Cột W - Hướng dẫn
    'precautions': 23,           # Cột X - Thận trọng
    'manufacturing_info': 24,    # Cột Y - Thông tin sản xuất
    'effect': 25,                # Cột Z - Công dụng (không lưu)
    'side_effect': 26,           # Cột AA - Tác dụng phụ (không lưu)
    'usage_instructions_2': 27,  # Cột AB - Hướng dẫn sử dụng (trùng)
    'note_2': 28,                # Cột AC - Lưu ý (trùng)
    'storage': 29,               # Cột AD - Bảo quản (không lưu)
}

# ===== CẤU HÌNH CÁC BẢNG CẦN INSERT =====
TABLES_TO_INSERT = [
    "products",
    "product_flat",
    "product_attribute_values",
    "product_categories",
    "product_images",
    "product_inventories"
]

def connect_to_google_sheets():
    """Kết nối Google Sheets"""
    print("🔗 Đang kết nối Google Sheets...")
    try:
        scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
        creds = ServiceAccountCredentials.from_json_keyfile_name(CREDENTIALS_FILE, scope)
        client = gspread.authorize(creds)
        sheet = client.open_by_key(SHEET_ID).worksheet(SHEET_NAME)
        print("✅ Đã kết nối Google Sheets thành công!")
        return sheet
    except Exception as e:
        print(f"❌ Lỗi kết nối Google Sheets: {e}")
        sys.exit(1)

def connect_to_mysql():
    """Kết nối MySQL Docker"""
    print("🔗 Đang kết nối MySQL Docker (Adminer)...")
    print(f"🖥️ Host: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"👤 User: {DB_CONFIG['user']}")
    print(f"🗄️ Database: {DB_CONFIG['database']}")

    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ Đã kết nối MySQL Docker thành công!")
        return connection
    except Exception as e:
        print(f"❌ Lỗi kết nối MySQL Docker: {e}")
        print("💡 Thử các cách sau:")
        print("   - Đảm bảo Docker đang chạy")
        print("   - Kiểm tra port 3307 có đúng không")
        print("   - Thử thay 'localhost' bằng 'mysql' hoặc '127.0.0.1'")
        print("   - Kiểm tra Adminer: http://localhost:8080")
        sys.exit(1)

def check_tables_exist(connection):
    """Kiểm tra các bảng products trong Docker MySQL"""
    for table_name in TABLES_TO_INSERT:
        print(f"🔧 Kiểm tra table {table_name} trong Docker MySQL...")

        # Kiểm tra xem table đã tồn tại chưa
        check_table_sql = f"""
        SELECT COUNT(*) as count
        FROM information_schema.tables
        WHERE table_schema = '{DB_CONFIG['database']}'
        AND table_name = '{table_name}'
        """

        try:
            with connection.cursor() as cursor:
                cursor.execute(check_table_sql)
                result = cursor.fetchone()

                if result['count'] > 0:
                    print(f"✅ Table {table_name} đã tồn tại trong Docker!")

                    # Kiểm tra cấu trúc table
                    cursor.execute(f"DESCRIBE {table_name}")
                    columns = cursor.fetchall()
                    print(f"📋 Cấu trúc table {table_name}:")
                    for col in columns[:5]:  # Chỉ hiển thị 5 cột đầu
                        print(f"  - {col['Field']}: {col['Type']}")
                    if len(columns) > 5:
                        print(f"  ... và {len(columns) - 5} cột khác")
                    print()
                else:
                    print(f"❌ Table {table_name} chưa tồn tại trong Docker!")
                    print("💡 Vui lòng tạo table trong Adminer trước: http://localhost:8080")
                    sys.exit(1)

        except Exception as e:
            print(f"❌ Lỗi khi kiểm tra table {table_name}: {e}")
            sys.exit(1)

def get_data_from_sheet(sheet):
    """Lấy dữ liệu từ Google Sheets"""
    print("📋 Đang lấy dữ liệu từ Google Sheets...")

    try:
        # Lấy tất cả dữ liệu từ sheet
        all_data = sheet.get_all_values()

        # Tính toán số dòng thực tế có data (bỏ qua header)
        total_rows_with_header = len(all_data)
        total_data_rows = total_rows_with_header - (START_ROW - 1)

        print(f"📊 Sheet có tổng cộng {total_rows_with_header} dòng (bao gồm header)")
        print(f"📊 Số dòng data thực tế: {total_data_rows} dòng")

        # Bỏ qua header
        data_rows = all_data[START_ROW-1:]

        # Giới hạn số lượng dòng nếu cần
        if ROW_NUMBER:
            if ROW_NUMBER > total_data_rows:
                print(f"⚠️  ROW_NUMBER ({ROW_NUMBER}) lớn hơn số dòng data thực tế ({total_data_rows})")
                print(f"📋 Sẽ xử lý TẤT CẢ {total_data_rows} dòng data có sẵn")
            else:
                print(f"📋 Giới hạn xử lý {ROW_NUMBER} dòng đầu tiên từ {total_data_rows} dòng data")
                data_rows = data_rows[:ROW_NUMBER]
        else:
            print(f"📋 Sẽ xử lý TẤT CẢ {total_data_rows} dòng data")

        actual_rows_to_process = len(data_rows)
        print(f"✅ Đã lấy {actual_rows_to_process} dòng dữ liệu từ Google Sheets để xử lý")

        return data_rows
    except Exception as e:
        print(f"❌ Lỗi khi lấy dữ liệu từ Google Sheets: {e}")
        sys.exit(1)

# ===== UTILITY FUNCTIONS =====

def ensure_directory_exists(directory_path):
    """Tạo thư mục nếu chưa tồn tại"""
    if not os.path.exists(directory_path):
        os.makedirs(directory_path)
        print(f"📁 Đã tạo thư mục: {directory_path}")

def download_image(image_url, save_directory):
    """Download ảnh từ URL và trả về tên file"""
    try:
        if not image_url or image_url.strip() == "":
            return None

        # Tạo tên file unique
        file_extension = ".png"  # Mặc định .png
        try:
            parsed_url = urlparse(image_url)
            original_extension = os.path.splitext(parsed_url.path)[1]
            if original_extension in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
                file_extension = original_extension
        except:
            pass

        filename = f"{uuid.uuid4().hex}{file_extension}"
        file_path = os.path.join(save_directory, filename)

        # Download ảnh
        response = requests.get(image_url, timeout=30)
        response.raise_for_status()

        with open(file_path, 'wb') as f:
            f.write(response.content)

        print(f"  📥 Downloaded: {filename}")
        return filename

    except Exception as e:
        print(f"  ❌ Lỗi download ảnh {image_url}: {e}")
        return None

def get_column_value(row, column_name):
    """Lấy giá trị từ cột theo tên"""
    column_index = COLUMN_MAPPING.get(column_name)
    if column_index is None:
        return ""

    if len(row) > column_index:
        return row[column_index].strip() if row[column_index] else ""
    return ""

def clean_price(price_string):
    """Làm sạch giá từ format '5.180 ₫' hoặc '4.400 ₫' thành float (dấu chấm là phân cách hàng nghìn)"""
    if not price_string:
        return 0.0

    try:
        # Loại bỏ ký tự ₫ và khoảng trắng
        cleaned = price_string.replace('₫', '').replace(' ', '').strip()

        # Loại bỏ dấu phẩy nếu có (ví dụ: 1,000 -> 1000)
        cleaned = cleaned.replace(',', '')

        # Loại bỏ dấu chấm (phân cách hàng nghìn) - ví dụ: 5.180 -> 5180
        cleaned = cleaned.replace('.', '')

        # Chuyển đổi thành float
        return float(cleaned)
    except:
        return 0.0

# ===== HELPER FUNCTIONS FOR DATABASE OPERATIONS =====

def get_brand_id(connection, brand_name):
    """Lấy brand_id từ bảng brands theo tên, tự động tạo mới nếu chưa có (theo quy trình đầy đủ)"""
    try:
        # Bỏ qua nếu giá trị là "Không có"
        if not brand_name or brand_name.strip().lower() == "không có":
            return None

        with connection.cursor() as cursor:
            # Bước 1: Kiểm tra brand đã tồn tại chưa
            sql = "SELECT id FROM brands WHERE brand_name = %s"
            cursor.execute(sql, (brand_name,))
            result = cursor.fetchone()

            if result:
                print(f"        ✅ Tìm thấy brand_id={result['id']} cho '{brand_name}'")
                return result['id']

            # Bước 2: Tạo brand mới theo quy trình đầy đủ (attribute_options + brands)
            print(f"        🆕 Tạo brand mới: '{brand_name}'")

            # Bước 2a: Insert vào attribute_options trước (attribute_id=43)
            attr_sql = """
            INSERT INTO attribute_options (attribute_id, admin_name, sort_order, swatch_value, is_featured, brand_image, brand_description)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(attr_sql, (43, brand_name, 1, None, 0, None, None))
            attribute_option_id = cursor.lastrowid
            print(f"        ✅ Inserted attribute_options: ID={attribute_option_id}, admin_name='{brand_name}'")

            # Bước 2b: Insert vào brands với attribute_option_id
            brand_sql = """
            INSERT INTO brands (attribute_option_id, brand_name, brand_image, brand_description, outstanding_product_id)
            VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(brand_sql, (attribute_option_id, brand_name, None, None, None))

            # Lấy brand_id vừa tạo
            new_brand_id = cursor.lastrowid
            print(f"        ✅ Inserted brands: ID={new_brand_id}, brand_name='{brand_name}', attribute_option_id={attribute_option_id}")

            return new_brand_id

    except Exception as e:
        print(f"  ❌ Lỗi khi xử lý brand '{brand_name}': {e}")
        return None

def get_unit_type_id(connection, unit_type_name):
    """Lấy unit_type_id từ bảng attribute_options theo tên đơn vị"""
    try:
        with connection.cursor() as cursor:
            # Bước 1: Lấy attribute_id của unit_type
            attr_sql = "SELECT id FROM attributes WHERE code = 'unit_type'"
            cursor.execute(attr_sql)
            attr_result = cursor.fetchone()

            if not attr_result:
                print(f"        ⚠️ Không tìm thấy attribute 'unit_type'")
                return None

            unit_type_attribute_id = attr_result['id']

            # Bước 2: Lấy id từ attribute_options
            option_sql = """
            SELECT id FROM attribute_options
            WHERE attribute_id = %s AND admin_name = %s
            """
            cursor.execute(option_sql, (unit_type_attribute_id, unit_type_name))
            option_result = cursor.fetchone()

            return option_result['id'] if option_result else None

    except Exception as e:
        print(f"  ❌ Lỗi khi lấy unit_type_id cho '{unit_type_name}': {e}")
        return None

def get_category_id(connection, category_name):
    """Lấy category_id từ bảng categories theo tên"""
    try:
        with connection.cursor() as cursor:
            # Tìm category theo name trong category_translations
            sql = """
            SELECT c.id
            FROM categories c
            JOIN category_translations ct ON c.id = ct.category_id
            WHERE ct.name = %s AND ct.locale = 'en'
            """
            cursor.execute(sql, (category_name,))
            result = cursor.fetchone()
            return result['id'] if result else None
    except Exception as e:
        print(f"  ❌ Lỗi khi lấy category_id cho '{category_name}': {e}")
        return None

def get_attribute_id(connection, attribute_code):
    """Lấy attribute_id từ bảng attributes theo code"""
    try:
        with connection.cursor() as cursor:
            sql = "SELECT id FROM attributes WHERE code = %s"
            cursor.execute(sql, (attribute_code,))
            result = cursor.fetchone()
            return result['id'] if result else None
    except Exception as e:
        print(f"  ❌ Lỗi khi lấy attribute_id cho '{attribute_code}': {e}")
        return None

def check_sku_exists(connection, sku):
    """Kiểm tra SKU đã tồn tại chưa"""
    try:
        with connection.cursor() as cursor:
            sql = "SELECT id FROM products WHERE sku = %s"
            cursor.execute(sql, (sku,))
            result = cursor.fetchone()
            return result['id'] if result else None
    except Exception as e:
        print(f"  ❌ Lỗi khi kiểm tra SKU '{sku}': {e}")
        return None

# ===== MAIN INSERT FUNCTIONS =====

def insert_product(connection, row):
    """Insert sản phẩm vào bảng products"""
    try:
        sku = get_column_value(row, 'sku')

        # Kiểm tra SKU đã tồn tại chưa
        existing_id = check_sku_exists(connection, sku)
        if existing_id:
            print(f"    ⚠️ SKU '{sku}' đã tồn tại với ID={existing_id}")
            return existing_id

        with connection.cursor() as cursor:
            sql = """
            INSERT INTO products (sku, type, parent_id, attribute_family_id, created_at, updated_at)
            VALUES (%s, %s, %s, %s, NOW(), NOW())
            """
            values = (sku, 'simple', None, 1)
            cursor.execute(sql, values)

            product_id = cursor.lastrowid
            print(f"      ✅ Inserted products: ID={product_id}, SKU='{sku}'")
            return product_id

    except Exception as e:
        print(f"    ❌ Lỗi khi insert product: {e}")
        raise e

def process_device_data(connection, row):
    """Xử lý dữ liệu cho 1 thiết bị y tế"""
    # Lấy dữ liệu cơ bản
    name = get_column_value(row, 'name')
    sku = get_column_value(row, 'sku')

    # Kiểm tra dữ liệu hợp lệ
    if not name or not sku:
        return None

    print(f"    📦 Xử lý thiết bị y tế: '{name}' (SKU: {sku})")

    # Tạo dữ liệu cho products table
    product_data = {
        'sku': sku,
        'type': 'simple',  # Mặc định simple
        'attribute_family_id': 1,  # Mặc định 1
        'parent_id': None,
        'created_at': 'NOW()',
        'updated_at': 'NOW()'
    }

    # Tạo dữ liệu cho product_flat table (chỉ các cột cần thiết)
    product_flat_data = {
        'sku': sku,
        'name': name,
        'description': get_column_value(row, 'description'),
        'short_description': get_column_value(row, 'summary'),
        'url_key': sku.lower().replace(' ', '-'),
        'new': 1,
        'featured': 0,
        'status': 1,
        'visible_individually': 1,
        'price': clean_price(get_column_value(row, 'price')),
        'locale': 'en',
        'channel': 'default',
        'product_id': None,  # Sẽ được set sau khi insert products
        'parent_id': None,
        'created_at': 'NOW()',
        'updated_at': 'NOW()'
    }

    return {
        'product_data': product_data,
        'product_flat_data': product_flat_data,
        'raw_row': row
    }

def insert_device_basic(connection, processed_data):
    """Insert vào products và product_flat tables"""
    try:
        with connection.cursor() as cursor:
            # Bước 1: Insert vào products
            product_data = processed_data['product_data']
            product_columns = list(product_data.keys())
            product_values = list(product_data.values())

            # Xử lý NOW() cho timestamps
            product_sql = f"INSERT INTO products ({', '.join(product_columns)}) VALUES ({', '.join(['NOW()' if v == 'NOW()' else '%s' for v in product_values])})"
            product_values_filtered = [v for v in product_values if v != 'NOW()']
            cursor.execute(product_sql, product_values_filtered)

            # Lấy product_id vừa insert
            product_id = cursor.lastrowid
            print(f"      ✅ Inserted products: ID={product_id}, SKU='{product_data['sku']}'")

            # Bước 2: Insert vào product_flat
            product_flat_data = processed_data['product_flat_data']
            product_flat_data['product_id'] = product_id  # Set product_id

            flat_columns = list(product_flat_data.keys())
            flat_values = list(product_flat_data.values())

            # Xử lý NOW() cho timestamps
            flat_sql = f"INSERT INTO product_flat ({', '.join(flat_columns)}) VALUES ({', '.join(['NOW()' if v == 'NOW()' else '%s' for v in flat_values])})"
            flat_values_filtered = [v for v in flat_values if v != 'NOW()']
            cursor.execute(flat_sql, flat_values_filtered)

            print(f"      ✅ Inserted product_flat: product_id={product_id}, name='{product_flat_data['name']}'")

            return product_id

    except Exception as e:
        print(f"      ❌ Lỗi khi insert device basic: {e}")
        raise e

def insert_product_attributes(connection, product_id, row):
    """Insert các attributes của sản phẩm vào product_attribute_values (LOẠI BỎ active_ingredient và usage)"""
    try:
        with connection.cursor() as cursor:
            # Mapping các attribute cần insert (LOẠI BỎ active_ingredient và usage)
            attribute_mappings = {
                'brand': {'column': 'brand', 'value_type': 'integer_value', 'get_id_func': get_brand_id},
                'price': {'column': 'price', 'value_type': 'float_value', 'get_id_func': None},
                'unit_type': {'column': 'unit_type', 'value_type': 'integer_value', 'get_id_func': get_unit_type_id},
                'manufacturer': {'column': 'manufacturer', 'value_type': 'text_value', 'get_id_func': None},
                # 'active_ingredient': LOẠI BỎ
                'indication': {'column': 'indication', 'value_type': 'text_value', 'get_id_func': None},
                'dosage_form': {'column': 'dosage_form', 'value_type': 'text_value', 'get_id_func': None},
                'packing': {'column': 'packing', 'value_type': 'text_value', 'get_id_func': None},
                # 'usage': LOẠI BỎ
                'note': {'column': 'note', 'value_type': 'text_value', 'get_id_func': None},
                'description': {'column': 'description', 'value_type': 'text_value', 'get_id_func': None},
                'ingredient': {'column': 'ingredient', 'value_type': 'text_value', 'get_id_func': None},
                'usage_instructions': {'column': 'usage_instructions', 'value_type': 'text_value', 'get_id_func': None},
                'precautions': {'column': 'precautions', 'value_type': 'text_value', 'get_id_func': None},
                'manufacturing_info': {'column': 'manufacturing_info', 'value_type': 'text_value', 'get_id_func': None},
            }

            inserted_count = 0

            for attribute_code, mapping in attribute_mappings.items():
                try:
                    # Lấy attribute_id
                    attribute_id = get_attribute_id(connection, attribute_code)
                    if not attribute_id:
                        print(f"        ⚠️ Không tìm thấy attribute '{attribute_code}'")
                        continue

                    # Lấy giá trị từ cột
                    column_value = get_column_value(row, mapping['column'])
                    if not column_value or column_value.lower() == "không có":
                        continue

                    # Xử lý giá trị theo loại
                    insert_value = None
                    if mapping['get_id_func']:
                        # Cần lookup ID (brand, unit_type)
                        insert_value = mapping['get_id_func'](connection, column_value)
                        if not insert_value:
                            continue
                    elif mapping['value_type'] == 'float_value':
                        # Xử lý giá
                        insert_value = clean_price(column_value)
                    else:
                        # Text value
                        insert_value = column_value

                    # Insert vào product_attribute_values
                    sql = f"""
                    INSERT INTO product_attribute_values (product_id, attribute_id, {mapping['value_type']}, locale, channel)
                    VALUES (%s, %s, %s, %s, %s)
                    """
                    cursor.execute(sql, (product_id, attribute_id, insert_value, 'en', 'default'))
                    inserted_count += 1

                except Exception as e:
                    print(f"        ❌ Lỗi khi insert attribute '{attribute_code}': {e}")
                    continue

            print(f"      ✅ Inserted {inserted_count} product attributes")

    except Exception as e:
        print(f"    ❌ Lỗi khi insert product attributes: {e}")
        raise e

def insert_product_categories(connection, product_id, row):
    """Insert categories cho sản phẩm vào product_categories (many-to-many)"""
    try:
        category_string = get_column_value(row, 'category')

        # Bỏ qua nếu không có category hoặc "Không có"
        if not category_string or category_string.lower() == "không có":
            print(f"      ⏭️ Bỏ qua categories: Không có dữ liệu")
            return

        # Tách categories nếu có dấu ";"
        categories = [cat.strip() for cat in category_string.split(';') if cat.strip()]

        with connection.cursor() as cursor:
            inserted_count = 0

            for category_name in categories:
                category_id = get_category_id(connection, category_name)
                if category_id:
                    # Insert vào product_categories
                    sql = "INSERT INTO product_categories (product_id, category_id) VALUES (%s, %s)"
                    cursor.execute(sql, (product_id, category_id))
                    inserted_count += 1
                else:
                    print(f"        ⚠️ Không tìm thấy category '{category_name}'")

            print(f"      ✅ Inserted {inserted_count} product categories")

    except Exception as e:
        print(f"    ❌ Lỗi khi insert product categories: {e}")
        raise e

def find_local_images(sku, image_directory):
    """Tìm ảnh local theo SKU pattern"""
    try:
        if not os.path.exists(image_directory):
            return []

        # Tìm tất cả file có chứa SKU trong tên
        matching_files = []
        for filename in os.listdir(image_directory):
            if sku.lower() in filename.lower() and filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
                matching_files.append(filename)

        # Sắp xếp theo tên để có thứ tự nhất quán
        matching_files.sort()
        return matching_files

    except Exception as e:
        print(f"  ❌ Lỗi khi tìm ảnh local cho SKU '{sku}': {e}")
        return []

def insert_product_images(connection, product_id, row):
    """Insert ảnh sản phẩm từ local directory"""
    try:
        sku = get_column_value(row, 'sku')

        # Tìm ảnh local theo SKU
        local_images = find_local_images(sku, LOCAL_IMAGE_PATH)

        if not local_images:
            print(f"      ⚠️ Không tìm thấy ảnh local cho SKU '{sku}'")
            return

        print(f"      🔍 Tìm thấy {len(local_images)} ảnh cho SKU '{sku}': {local_images[:3]}{'...' if len(local_images) > 3 else ''}")

        with connection.cursor() as cursor:
            inserted_count = 0

            for position, filename in enumerate(local_images, start=1):
                try:
                    # Path để lưu vào DB (với prefix)
                    db_path = IMAGE_URL_PREFIX + filename

                    # Insert vào product_images
                    sql = """
                    INSERT INTO product_images (type, path, product_id, position)
                    VALUES (%s, %s, %s, %s)
                    """
                    cursor.execute(sql, ('image', db_path, product_id, position))
                    inserted_count += 1

                except Exception as e:
                    print(f"        ❌ Lỗi khi insert ảnh '{filename}': {e}")
                    continue

            print(f"      ✅ Inserted {inserted_count} product images từ local")

    except Exception as e:
        print(f"    ❌ Lỗi khi insert product images: {e}")
        raise e

def insert_product_inventory(connection, product_id):
    """Insert inventory cho sản phẩm"""
    try:
        with connection.cursor() as cursor:
            sql = """
            INSERT INTO product_inventories (qty, product_id, vendor_id, inventory_source_id)
            VALUES (%s, %s, %s, %s)
            """
            cursor.execute(sql, (100, product_id, 0, 1))

            print(f"      ✅ Inserted product inventory: qty=100, product_id={product_id}")

    except Exception as e:
        print(f"    ❌ Lỗi khi insert product inventory: {e}")
        raise e

def insert_random_attribute(connection, product_id):
    """Insert random attribute với attribute_id=45"""
    try:
        with connection.cursor() as cursor:
            # Random integer_value (22 hoặc 23)
            random_value = random.choice([22, 23])
            unique_id = f"{product_id}|45"

            sql = """
            INSERT INTO product_attribute_values (product_id, attribute_id, integer_value, unique_id, locale, channel)
            VALUES (%s, %s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (product_id, 45, random_value, unique_id, None, None))

            print(f"      ✅ Inserted random attribute: product_id={product_id}, attribute_id=45, integer_value={random_value}, unique_id='{unique_id}'")

    except Exception as e:
        print(f"    ❌ Lỗi khi insert random attribute: {e}")
        raise e

def insert_boolean_attribute(connection, product_id):
    """Insert boolean attribute với attribute_id=46"""
    try:
        with connection.cursor() as cursor:
            unique_id = f"{product_id}|46"

            sql = """
            INSERT INTO product_attribute_values (product_id, attribute_id, boolean_value, unique_id, locale, channel)
            VALUES (%s, %s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (product_id, 46, 1, unique_id, None, None))

            print(f"      ✅ Inserted boolean attribute: product_id={product_id}, attribute_id=46, boolean_value=1, unique_id='{unique_id}'")

    except Exception as e:
        print(f"    ❌ Lỗi khi insert boolean attribute: {e}")
        raise e

# ===== MAIN WORKFLOW =====

def insert_devices_workflow(connection, data_rows):
    """Insert workflow: devices → device_flat → attributes → categories → images → inventories → random_attr → boolean_attr"""
    print("🚀 BẮT ĐẦU WORKFLOW INSERT MEDICAL DEVICES (DOCKER)")
    print("📋 Workflow: products → product_flat → attributes → categories → images → inventories → random_attr → boolean_attr")
    print("⚠️ LOẠI BỎ: số đăng kí, hoạt chất, đối tượng sử dụng, cách dùng")

    total_success = 0
    total_failed = 0
    total_skipped = 0

    try:
        with connection.cursor() as cursor:
            for row_index, row in enumerate(data_rows, start=1):
                try:
                    print(f"\n🔄 Xử lý dòng {row_index}:")

                    # Lấy SKU để kiểm tra
                    sku = get_column_value(row, 'sku')
                    if not sku:
                        print(f"  ⏭️ Bỏ qua dòng {row_index}: Không có SKU")
                        total_skipped += 1
                        continue

                    print(f"  📦 Mã sản phẩm: '{sku}'")

                    # Bước 1: Kiểm tra cột S (Tổng hợp) - Bỏ qua nếu khác "Không có"
                    summary_value = get_column_value(row, 'summary')
                    if summary_value and summary_value.strip().lower() != "không có":
                        print(f"  ⏭️ Bỏ qua dòng {row_index}: Cột S (Tổng hợp) có giá trị '{summary_value}' - cần xử lý riêng")
                        total_skipped += 1
                        continue

                    # Bước 2: Kiểm tra SKU đã tồn tại chưa
                    existing_id = check_sku_exists(connection, sku)
                    if existing_id:
                        print(f"    ⚠️ SKU '{sku}' đã tồn tại với ID={existing_id}")
                        total_skipped += 1
                        continue

                    # Bước 3: Xử lý dữ liệu thiết bị y tế
                    processed_data = process_device_data(connection, row)
                    if not processed_data:
                        print(f"  ⏭️ Bỏ qua dòng {row_index}: Dữ liệu thiết bị y tế không hợp lệ")
                        total_skipped += 1
                        continue

                    # Bước 4: Insert products và product_flat
                    product_id = insert_device_basic(connection, processed_data)

                    # Bước 5: Insert attributes (LOẠI BỎ active_ingredient và usage)
                    insert_product_attributes(connection, product_id, row)

                    # Bước 6: Insert categories
                    insert_product_categories(connection, product_id, row)

                    # Bước 7: Insert images từ local
                    insert_product_images(connection, product_id, row)

                    # Bước 8: Insert inventory
                    insert_product_inventory(connection, product_id)

                    # Bước 9: Insert random attribute
                    insert_random_attribute(connection, product_id)

                    # Bước 10: Insert boolean attribute
                    insert_boolean_attribute(connection, product_id)

                    # Commit transaction cho từng sản phẩm
                    connection.commit()
                    total_success += 1
                    print(f"  ✅ Hoàn thành thiết bị y tế: ID={product_id}, SKU='{sku}'")

                except Exception as e:
                    print(f"  ❌ Lỗi khi xử lý dòng {row_index}: {e}")
                    total_failed += 1
                    connection.rollback()
                    continue

        print(f"\n📊 KẾT QUẢ WORKFLOW MEDICAL DEVICES (DOCKER):")
        print(f"  ✅ Thành công: {total_success} devices")
        print(f"  ⏭️ Bỏ qua: {total_skipped} devices")
        print(f"  ❌ Thất bại: {total_failed} devices")

    except Exception as e:
        print(f"❌ Lỗi workflow: {e}")
        connection.rollback()

def check_existing_data(connection):
    """Kiểm tra dữ liệu hiện có trong các tables"""
    for table_name in TABLES_TO_INSERT:
        print(f"🔍 Kiểm tra dữ liệu hiện có trong table {table_name} (Docker)...")

        try:
            with connection.cursor() as cursor:
                cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                result = cursor.fetchone()
                count = result['count']
                print(f"📊 Hiện có {count} records trong table {table_name}")
                print()
        except Exception as e:
            print(f"❌ Lỗi khi kiểm tra dữ liệu {table_name}: {e}")

def main():
    """Hàm main cho Medical Devices workflow"""
    print("🚀 BẮT ĐẦU INSERT MEDICAL DEVICES VÀO DOCKER MYSQL (ADMINER)")
    print("="*60)
    print(f"📊 Cấu hình: Insert tối đa {ROW_NUMBER if ROW_NUMBER else 'TẤT CẢ'} dòng")
    print(f"🐳 Docker Database: {DB_CONFIG['database']}")
    print(f"🖥️ Docker Host: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"📋 Active Workflow: Medical Devices Multi-Tables")
    print(f"📋 Tables: {', '.join(TABLES_TO_INSERT)}")
    print(f"🖼️ Local Images Path: {LOCAL_IMAGE_PATH}")
    print(f"⚠️ LOẠI BỎ: số đăng kí, hoạt chất, đối tượng sử dụng, cách dùng")
    print(f"🌐 Adminer URL: http://localhost:8080")
    print("="*60)

    try:
        # Kết nối Google Sheets
        sheet = connect_to_google_sheets()

        # Kết nối Docker MySQL
        connection = connect_to_mysql()

        # Kiểm tra tables trong Docker
        check_tables_exist(connection)

        # Kiểm tra dữ liệu hiện có
        check_existing_data(connection)

        # Lấy dữ liệu từ Google Sheets
        data_rows = get_data_from_sheet(sheet)

        # Insert dữ liệu vào Docker MySQL
        insert_devices_workflow(connection, data_rows)

        # Kiểm tra lại sau khi insert
        print("\n🔍 Kiểm tra lại sau khi insert:")
        check_existing_data(connection)

        # Đóng kết nối
        connection.close()

        print("\n" + "="*60)
        print("🎉 HOÀN TẤT INSERT MEDICAL DEVICES VÀO DOCKER!")
        print("🌐 Kiểm tra kết quả tại: http://localhost:8080")
        print("="*60)

    except Exception as e:
        print(f"❌ Lỗi không mong muốn: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()