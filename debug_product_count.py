from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import re

def init_browser():
    """Khởi tạo browser với cấu hình tối ưu"""
    print("🚀 Khởi tạo Chrome browser...")
    service = Service(ChromeDriverManager().install())
    
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--disable-images")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--log-level=3")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    print("✅ Chrome browser đã sẵn sàng!")
    return driver

def debug_product_selectors(driver, url):
    """Debug các selector có thể dùng để tìm sản phẩm"""
    print(f"\n🔍 Debug selectors cho: {url}")
    driver.get(url)
    time.sleep(5)
    
    soup = BeautifulSoup(driver.page_source, "html.parser")
    
    # Test các selector khác nhau
    selectors_to_test = [
        "[data-testid='product-card']",
        ".product-card",
        "[data-testid='product-item']", 
        ".product-item",
        "a[href*='.html']",
        "a[href*='/product/']",
        ".product",
        "[class*='product']",
        "div[class*='card']",
        "div[class*='item']"
    ]
    
    print("\n📋 Kết quả test selectors:")
    for selector in selectors_to_test:
        try:
            elements = soup.select(selector)
            print(f"  {selector:<30} → {len(elements)} elements")
            
            # Hiển thị một vài ví dụ
            if len(elements) > 0:
                for i, elem in enumerate(elements[:3]):
                    if elem.get('href'):
                        print(f"    Ví dụ {i+1}: {elem.get('href')}")
                    elif elem.find('a'):
                        link = elem.find('a')
                        if link and link.get('href'):
                            print(f"    Ví dụ {i+1}: {link.get('href')}")
        except Exception as e:
            print(f"  {selector:<30} → Lỗi: {e}")

def advanced_load_more_products(driver, max_attempts=50):
    """Hàm load thêm sản phẩm cải tiến"""
    print("\n🔄 Bắt đầu load thêm sản phẩm...")
    previous_count = 0
    attempt = 0
    no_change_count = 0
    
    while attempt < max_attempts:
        try:
            # Test nhiều selector khác nhau để đếm sản phẩm
            product_selectors = [
                "[data-testid='product-card']",
                ".product-card", 
                "a[href*='.html']",
                "div[class*='product']"
            ]
            
            current_count = 0
            for selector in product_selectors:
                try:
                    products = driver.find_elements(By.CSS_SELECTOR, selector)
                    if len(products) > current_count:
                        current_count = len(products)
                        best_selector = selector
                except:
                    continue
            
            print(f"  Lần {attempt+1}: {current_count} sản phẩm (selector: {best_selector if current_count > 0 else 'none'})")
            
            # Nếu không có thay đổi
            if current_count == previous_count:
                no_change_count += 1
                
                # Thử các cách load thêm khác nhau
                load_more_methods = [
                    # Phương pháp 1: Tìm nút "Xem thêm"
                    lambda: driver.find_element(By.XPATH, "//button[contains(text(), 'Xem thêm') or contains(text(), 'Load more') or contains(text(), 'Tải thêm')]"),
                    # Phương pháp 2: Tìm nút có class chứa "load" hoặc "more"
                    lambda: driver.find_element(By.CSS_SELECTOR, "button[class*='load'], button[class*='more']"),
                    # Phương pháp 3: Scroll xuống cuối trang
                    lambda: driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                ]
                
                success = False
                for i, method in enumerate(load_more_methods):
                    try:
                        if i < 2:  # Các phương pháp click button
                            btn = method()
                            driver.execute_script("arguments[0].scrollIntoView();", btn)
                            time.sleep(1)
                            btn.click()
                            print(f"    ✅ Đã click nút load more (phương pháp {i+1})")
                        else:  # Phương pháp scroll
                            method()
                            print(f"    ✅ Đã scroll xuống cuối trang")
                        
                        time.sleep(3)
                        success = True
                        break
                    except Exception as e:
                        print(f"    ❌ Phương pháp {i+1} thất bại: {e}")
                        continue
                
                if not success:
                    print(f"    ⚠️ Không thể load thêm sản phẩm")
                    
                # Nếu không có thay đổi sau 3 lần thử
                if no_change_count >= 3:
                    print(f"    🛑 Dừng sau {no_change_count} lần không có thay đổi")
                    break
            else:
                no_change_count = 0
            
            previous_count = current_count
            attempt += 1
            
        except Exception as e:
            print(f"    ❌ Lỗi khi load thêm sản phẩm: {e}")
            break
    
    print(f"✅ Hoàn thành load sản phẩm: {current_count} sản phẩm sau {attempt} lần thử")
    return current_count

def count_products_in_category(driver, category_url):
    """Đếm số sản phẩm thực tế trong một danh mục"""
    print(f"\n{'='*80}")
    print(f"🏷️ PHÂN TÍCH DANH MỤC: {category_url}")
    print(f"{'='*80}")
    
    driver.get(category_url)
    time.sleep(5)
    
    # Debug selectors trước
    debug_product_selectors(driver, category_url)
    
    # Load thêm sản phẩm
    total_products = advanced_load_more_products(driver)
    
    # Lấy HTML cuối cùng và phân tích links
    soup = BeautifulSoup(driver.page_source, "html.parser")
    
    # Tìm tất cả link sản phẩm
    product_links = []
    all_links = soup.find_all("a", href=True)
    
    for link in all_links:
        href = link.get("href")
        if href and (".html" in href or "/product/" in href):
            if href.startswith("/"):
                product_url = "https://www.pharmacity.vn" + href
            elif href.startswith("https://www.pharmacity.vn"):
                product_url = href
            else:
                continue
            
            if product_url not in product_links:
                product_links.append(product_url)
    
    print(f"\n📊 KẾT QUẢ PHÂN TÍCH:")
    print(f"  🔢 Tổng số elements tìm thấy: {total_products}")
    print(f"  🔗 Tổng số link sản phẩm unique: {len(product_links)}")
    
    # Hiển thị một vài ví dụ link
    print(f"\n📋 VÍ DỤ LINK SẢN PHẨM (5 đầu tiên):")
    for i, link in enumerate(product_links[:5]):
        print(f"  {i+1}. {link}")
    
    return len(product_links), product_links

def main():
    driver = init_browser()
    
    # Test với một vài danh mục chính
    test_categories = [
        "https://www.pharmacity.vn/thuoc-khong-ke-don",
        "https://www.pharmacity.vn/thuoc-vitamin-thuc-pham-chuc-nang",
        "https://www.pharmacity.vn/thuoc-cam-lanh"
    ]
    
    total_unique_products = set()
    
    for category in test_categories:
        try:
            count, links = count_products_in_category(driver, category)
            total_unique_products.update(links)
            print(f"\n✅ Danh mục này có {count} sản phẩm")
            
        except Exception as e:
            print(f"❌ Lỗi khi phân tích danh mục {category}: {e}")
    
    print(f"\n{'='*80}")
    print(f"📊 TỔNG KẾT")
    print(f"{'='*80}")
    print(f"🎯 Tổng số sản phẩm unique từ {len(test_categories)} danh mục: {len(total_unique_products)}")
    
    driver.quit()
    print("🎉 Hoàn thành phân tích!")

if __name__ == "__main__":
    main()
