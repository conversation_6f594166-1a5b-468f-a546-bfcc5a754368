import requests
from bs4 import BeautifulSoup
import re
import csv
import time

def crawl_product_detail(product_url):
    """Crawl chi tiết một sản phẩm"""
    try:
        print(f"🔍 Crawling: {product_url}")

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(product_url, headers=headers)
        response.raise_for_status()

        soup = BeautifulSoup(response.content, 'html.parser')

        # Khởi tạo các biến
        product_name = "Không có"
        product_code = "Không có"
        brand = "Không có"
        price = "Không có"
        unit = "Không có"
        category = "Không có"
        manufacturer = "Không có"
        registration_number = "Không có"
        prescription_required = "Không"
        active_ingredient = "Không có"
        indication = "Không có"
        target_user = "Người lớn"
        dosage_form = "Không có"
        specification = "Không có"
        usage = "Không có"
        notes = "Không có"
        product_images = "Không có"

        # Lấy tên sản phẩm
        name_selectors = [
            'h1[data-testid="product-name"]',
            'h1.product-name',
            'h1',
            '.product-title h1',
            '[data-testid="product-title"]'
        ]
        for selector in name_selectors:
            name_element = soup.select_one(selector)
            if name_element:
                product_name = name_element.text.strip()
                break

        # Lấy mã sản phẩm
        page_text = soup.get_text()
        if "P0" in page_text:
            code_match = re.search(r'P\d{5}', page_text)
            if code_match:
                product_code = code_match.group(0)

        # Lấy giá và đơn vị
        # Debug: tìm text chứa "86.000"
        context_match = re.search(r'.{20}86\.000.{20}', page_text)
        if context_match:
            print(f"🔍 Context: {repr(context_match.group(0))}")

        # Tìm giá có đơn vị cụ thể (ví dụ: "86.000 ₫/Kit")
        price_with_unit_patterns = [
            r'(\d+[.,]?\d*)\s*₫/(Kit|Hộp|Chai|Lọ|Vỉ|Gói|Bộ|Tuýp)',
            r'(\d+[.,]?\d*)\xa0₫/(Kit|Hộp|Chai|Lọ|Vỉ|Gói|Bộ|Tuýp)',
            r'(\d+[.,]?\d*)[\s\xa0]*₫/(Kit|Hộp|Chai|Lọ|Vỉ|Gói|Bộ|Tuýp)'
        ]

        found_price_unit = False
        for i, pattern in enumerate(price_with_unit_patterns):
            price_with_unit_match = re.search(pattern, page_text)
            if price_with_unit_match:
                price = price_with_unit_match.group(1) + " ₫"
                unit = price_with_unit_match.group(2)
                found_price_unit = True
                print(f"✅ Pattern {i+1} matched: {price_with_unit_match.groups()}")
                break
            else:
                print(f"❌ Pattern {i+1} failed: {pattern}")

        if not found_price_unit:
            # Tìm giá thông thường
            price_match = re.search(r'(\d+[.,]?\d*)[\s\xa0]*₫', page_text)
            if price_match:
                price = price_match.group(0)
                unit = "Không có"

        print(f"✅ Thành công: {product_name}")
        print(f"💰 Giá: {price}")
        print(f"📦 Đơn vị: {unit}")

        return [
            product_url, product_name, product_code, brand, price, unit, category,
            manufacturer, registration_number, prescription_required,
            active_ingredient, indication, target_user, dosage_form,
            specification, usage, notes, product_images
        ]

    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return None

def main():
    # Test với sản phẩm có đơn vị Kit
    test_urls = [
        "https://www.pharmacity.vn/genesign-test-respiratory-combo-antigen-rapid-20-test-hop.html"
    ]

    print(f"🚀 Test crawl sản phẩm có đơn vị Kit...")

    for url in test_urls:
        product_data = crawl_product_detail(url)
        if product_data:
            print(f"\n📊 Kết quả:")
            print(f"   Tên: {product_data[1]}")
            print(f"   Mã: {product_data[2]}")
            print(f"   Giá: {product_data[4]}")
            print(f"   Đơn vị: {product_data[5]}")

if __name__ == "__main__":
    main()
