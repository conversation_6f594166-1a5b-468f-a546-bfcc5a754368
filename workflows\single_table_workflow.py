# ===== SINGLE TABLE WORKFLOW =====

from workflows.base_workflow import BaseWorkflow
from processors.brands_processor import AttributeOptionsProcessor, ProductsProcessor

class SingleTableWorkflow(BaseWorkflow):
    """Workflow thông thường cho 1 bảng đơn lẻ"""
    
    def __init__(self, connection, config):
        super().__init__(connection, config)
        
        # Tạo processor dựa trên config
        processor_name = config.get('processor', 'AttributeOptionsProcessor')
        if processor_name == 'AttributeOptionsProcessor':
            self.processor = AttributeOptionsProcessor(config)
        elif processor_name == 'ProductsProcessor':
            self.processor = ProductsProcessor(config)
        else:
            raise ValueError(f"Processor '{processor_name}' không được hỗ trợ")
    
    def process_data_rows(self, data_rows):
        """Xử lý workflow cho 1 bảng đơn lẻ"""
        self.print_workflow_header()
        print(f"📋 Columns: {', '.join(self.config['columns'])}")
        
        try:
            for row_index, row in enumerate(data_rows, start=1):
                try:
                    # X<PERSON> lý dữ liệu bằng processor
                    processed_data = self.processor.process_row(row)
                    
                    if not processed_data:
                        self.total_skipped += 1
                        continue
                    
                    # Tạo unique key để check duplicate
                    unique_key = self.get_unique_key(processed_data)
                    display_name = self.get_display_name(processed_data, row_index)
                    
                    # Kiểm tra duplicate
                    if self.is_duplicate(unique_key):
                        self.total_skipped += 1
                        continue
                    
                    # Insert vào database
                    record = self.processor.create_record_for_insert(processed_data)
                    sql = self.processor.get_insert_sql()
                    record_id = self.execute_sql(sql, record)
                    
                    self.commit_transaction()
                    self.total_success += 1
                    print(f"  📦 Dòng {row_index}: {display_name} (ID={record_id})")
                    
                except Exception as e:
                    print(f"❌ Lỗi khi xử lý dòng {row_index}: {e}")
                    self.total_failed += 1
                    self.rollback_transaction()
                    continue
            
            # In kết quả
            self.print_workflow_results()
            
        except Exception as e:
            print(f"❌ Lỗi workflow: {e}")
            self.rollback_transaction()
    
    def get_unique_key(self, processed_data):
        """Tạo unique key để check duplicate"""
        if self.table_name == "attribute_options":
            return processed_data.get('admin_name', '')
        elif self.table_name == "products":
            return processed_data.get('product_code', '')
        else:
            return str(hash(str(processed_data)))
    
    def get_display_name(self, processed_data, row_index):
        """Tạo display name cho logging"""
        if self.table_name == "attribute_options":
            return processed_data.get('admin_name', f'Row {row_index}')
        elif self.table_name == "products":
            return processed_data.get('product_name', f'Row {row_index}')
        else:
            return f'Row {row_index}'
