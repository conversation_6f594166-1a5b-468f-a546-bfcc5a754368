import requests
from bs4 import BeautifulSoup
import re

def test_complete_updated_extraction(url):
    """Test toàn bộ logic extraction đã cập nhật"""
    print(f"🧪 Test toàn bộ extraction đã cập nhật: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        product_soup = BeautifulSoup(response.content, 'html.parser')
        
        # Test lấy tên sản phẩm
        product_name = "Không có"
        name_selectors = [
            'h1[data-testid="product-name"]',
            'h1.product-name',
            'h1',
            '.product-title h1',
            '[data-testid="product-title"]'
        ]
        for selector in name_selectors:
            name_element = product_soup.select_one(selector)
            if name_element:
                product_name = name_element.text.strip()
                break
        
        # Test lấy mã sản phẩm (logic mới)
        product_code = "Không có"
        
        # Phương pháp 1: Tìm span chứa mã sản phẩm bắt đầu bằng "P"
        all_spans = product_soup.find_all('span')
        for span in all_spans:
            span_text = span.get_text(strip=True)
            if re.match(r'^P\d{5,6}$', span_text):
                product_code = span_text
                break
        
        # Phương pháp 2: Tìm trong text gần thương hiệu
        if product_code == "Không có":
            brand_link = product_soup.find('a', href=lambda x: x and '/thuong-hieu/' in x)
            if brand_link:
                parent = brand_link.parent
                if parent:
                    parent_text = parent.get_text()
                    code_match = re.search(r'P\d{5,6}', parent_text)
                    if code_match:
                        product_code = code_match.group()
        
        # Phương pháp 3: Tìm trong toàn bộ text trang
        if product_code == "Không có":
            page_text = product_soup.get_text()
            code_match = re.search(r'P\d{5,6}', page_text)
            if code_match:
                product_code = code_match.group()
        
        # Test lấy thương hiệu
        brand = "Không có"
        brand_link = product_soup.find('a', href=lambda x: x and '/thuong-hieu/' in x)
        if brand_link:
            brand = brand_link.text.strip().replace("Thương hiệu: ", "")
        
        # Test lấy danh mục (logic mới)
        category = "Không có"
        
        # Tìm thẻ p có text "Danh mục" và lấy div ngay dưới nó
        danh_muc_elements = product_soup.find_all('p', string=lambda text: text and 'Danh mục' in text)
        
        for danh_muc_p in danh_muc_elements:
            next_div = danh_muc_p.find_next_sibling('div')
            if next_div:
                category_text = next_div.get_text(strip=True)
                if category_text and len(category_text) > 3:
                    category_text = re.sub(r'\s+', ' ', category_text)
                    category = category_text
                    break
        
        # Test lấy phân loại sản phẩm (logic mới)
        product_classifications = []
        classification_keywords = ['Kit', 'Hộp', 'Chai', 'Lọ', 'Vỉ', 'Gói', 'Bộ', 'Tuýp', 'Viên', 'Túi']
        
        # Tìm thẻ label có text "Phân loại sản phẩm"
        classification_labels = product_soup.find_all('label', string=lambda text: text and 'Phân loại sản phẩm' in text)
        
        for label in classification_labels:
            next_div = label.find_next_sibling('div')
            if next_div:
                buttons = next_div.find_all('button')
                for button in buttons:
                    spans = button.find_all('span')
                    for span in spans:
                        span_text = span.get_text(strip=True)
                        if span_text and not span_text.isdigit() and span_text not in product_classifications:
                            if any(keyword.lower() in span_text.lower() for keyword in classification_keywords):
                                product_classifications.append(span_text)
        
        # Gộp phân loại
        if product_classifications:
            product_classification = ", ".join(product_classifications)
        else:
            product_classification = "Không có"
        
        # In kết quả
        print(f"📋 Kết quả extraction:")
        print(f"   🏷️  Tên sản phẩm: {product_name}")
        print(f"   🔢 Mã sản phẩm: {product_code}")
        print(f"   🏢 Thương hiệu: {brand}")
        print(f"   📂 Danh mục: {category}")
        print(f"   📦 Phân loại: {product_classification}")
        
        return {
            'name': product_name,
            'code': product_code,
            'brand': brand,
            'category': category,
            'classification': product_classification
        }
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return None

if __name__ == "__main__":
    # Test với các sản phẩm thực tế
    test_urls = [
        "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html",
        "https://www.pharmacity.vn/genesign-test-respiratory-combo-antigen-rapid-20-test-hop.html",
    ]
    
    for i, test_url in enumerate(test_urls):
        print(f"\n{'='*80}")
        print(f"TEST {i+1}")
        result = test_complete_updated_extraction(test_url)
        
        if result:
            print(f"\n✅ Extraction thành công!")
            print(f"   📊 Mã sản phẩm có giá trị: {result['code'] != 'Không có'}")
            print(f"   📊 Danh mục có giá trị: {result['category'] != 'Không có'}")
            print(f"   📊 Phân loại có nhiều giá trị: {',' in result['classification']}")
        else:
            print(f"❌ Extraction thất bại!")
