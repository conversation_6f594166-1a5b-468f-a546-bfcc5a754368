# ===== GLOBAL SETTINGS =====

import os

# ===== CẤU HÌNH TEST =====
MAX_ROWS = 1  # 🎯 KIỂM SOÁT SỐ DÒNG: Số dòng tối đa muốn xử lý (None = tất cả dòng)
BATCH_SIZE = 10  # Số lượng records insert cùng lúc (không áp dụng cho brands workflow)
START_ROW = 2  # Bắt đầu từ dòng nào (2 = bỏ qua header)

# Cấu hình download ảnh
IMAGE_DOWNLOAD_PATH = r"C:\Working\Medical-EC\MEDICAL_EC_SYSTEM\storage\app\public\brands"
IMAGE_URL_PREFIX = "brands/"  # Prefix để lưu vào DB

# ===== CẤU HÌNH CÁC BẢNG =====
TABLE_CONFIGS = {
    "brands": {
        "table_name": "brands",
        "sheet_name": "Brands",
        "columns": ["attribute_option_id", "brand_name", "brand_image", "brand_description", "outstanding_product_id"],
        "mapping": {
            'brand_image': 0,        # Cột A - Image URL
            'brand_name': 1,         # Cột B - Brand Name
        },
        "processor": "BrandsProcessor",
        "workflow": "brands_workflow"  # Workflow đặc biệt cho brands
    },
    "attribute_options": {
        "table_name": "attribute_options",
        "sheet_name": "Brands",
        "columns": ["attribute_id", "admin_name", "sort_order", "swatch_value", "is_featured", "brand_image", "brand_description"],
        "mapping": {
            'admin_name': 1,         # Cột B - Brand Name từ sheet Brands
        },
        "processor": "AttributeOptionsProcessor",
        "workflow": "single_table_workflow"  # Workflow thông thường
    },
    "products": {
        "table_name": "products",
        "sheet_name": "Medicine",
        "columns": ["product_code", "product_name", "brand", "price", "category"],
        "mapping": {
            'product_code': 2,       # Cột C - Mã sản phẩm
            'product_name': 1,       # Cột B - Tên sản phẩm
            'brand': 3,              # Cột D - Thương hiệu
            'price': 4,              # Cột E - Giá
            'category': 6,           # Cột G - Danh mục
        },
        "processor": "ProductsProcessor",
        "workflow": "single_table_workflow"
    }
}

# Hàm helper để lấy config
def get_table_config(table_name):
    """Lấy config của table"""
    if table_name not in TABLE_CONFIGS:
        raise ValueError(f"Table '{table_name}' không có trong cấu hình")
    return TABLE_CONFIGS[table_name]

def get_available_tables():
    """Lấy danh sách các table có sẵn"""
    return list(TABLE_CONFIGS.keys())

def ensure_directory_exists(directory_path):
    """Tạo thư mục nếu chưa tồn tại"""
    if not os.path.exists(directory_path):
        os.makedirs(directory_path)
        print(f"📁 Đã tạo thư mục: {directory_path}")
        return True
    return False
