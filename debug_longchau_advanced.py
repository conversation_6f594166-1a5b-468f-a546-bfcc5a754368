from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time
from selenium.webdriver.common.by import By
import re

def init_browser():
    """Khởi tạo browser v<PERSON>i cấu hình tối ưu"""
    print("🚀 Khởi tạo Chrome browser...")
    service = Service(ChromeDriverManager().install())
    
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--disable-images")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--log-level=3")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    print("✅ Chrome browser đã sẵn sàng!")
    return driver

def test_search_functionality(driver):
    """Test chức năng tìm kiếm để ước tính tổng số sản phẩm"""
    print(f"\n{'='*80}")
    print(f"🔍 TEST CHỨC NĂNG TÌM KIẾM LONG CHÂU")
    print(f"{'='*80}")
    
    search_keywords = ["thuốc", "paracetamol", "vitamin", "kháng sinh"]
    
    for keyword in search_keywords:
        try:
            print(f"\n🔍 Tìm kiếm: '{keyword}'")
            
            # Thử nhiều URL tìm kiếm khác nhau
            search_urls = [
                f"https://nhathuoclongchau.com.vn/tim-kiem?q={keyword}",
                f"https://nhathuoclongchau.com.vn/search?keyword={keyword}",
                f"https://nhathuoclongchau.com.vn/search?q={keyword}"
            ]
            
            for search_url in search_urls:
                try:
                    print(f"  📄 Thử URL: {search_url}")
                    driver.get(search_url)
                    time.sleep(5)
                    
                    soup = BeautifulSoup(driver.page_source, "html.parser")
                    
                    # Tìm thông tin về số kết quả
                    result_patterns = [
                        r'(\d+)\s*kết quả',
                        r'(\d+)\s*sản phẩm',
                        r'(\d+)\s*results?',
                        r'(\d+)\s*products?',
                        r'Tìm thấy\s*(\d+)',
                        r'Found\s*(\d+)'
                    ]
                    
                    page_text = soup.get_text()
                    
                    for pattern in result_patterns:
                        matches = re.findall(pattern, page_text, re.IGNORECASE)
                        if matches:
                            print(f"    ✅ Tìm thấy: {matches} kết quả")
                            break
                    
                    # Đếm số sản phẩm thực tế trên trang
                    product_selectors = [".product-card", "[class*='product']", "a[href*='.html']"]
                    
                    for selector in product_selectors:
                        try:
                            products = soup.select(selector)
                            if len(products) > 0:
                                print(f"    📊 Selector '{selector}': {len(products)} sản phẩm")
                                
                                # Hiển thị vài ví dụ
                                for i, product in enumerate(products[:3]):
                                    if product.get('href'):
                                        print(f"      - {product.get('href')}")
                                    elif product.find('a'):
                                        link = product.find('a')
                                        if link and link.get('href'):
                                            print(f"      - {link.get('href')}")
                                break
                        except:
                            continue
                    
                    break  # Nếu thành công, không thử URL khác
                    
                except Exception as e:
                    print(f"    ❌ Lỗi với URL này: {e}")
                    continue
                    
        except Exception as e:
            print(f"❌ Lỗi tìm kiếm '{keyword}': {e}")

def test_pagination(driver, base_url):
    """Test pagination để xem có bao nhiêu trang"""
    print(f"\n🔍 Test pagination cho: {base_url}")
    
    driver.get(base_url)
    time.sleep(5)
    
    soup = BeautifulSoup(driver.page_source, "html.parser")
    
    # Tìm pagination
    pagination_selectors = [
        ".pagination",
        ".pager", 
        "[class*='pagination']",
        "[class*='pager']",
        "a[href*='page=']",
        "a[href*='p=']"
    ]
    
    max_page = 1
    
    for selector in pagination_selectors:
        try:
            elements = soup.select(selector)
            if elements:
                print(f"  📄 Tìm thấy pagination với selector: {selector}")
                
                # Tìm số trang cao nhất
                for elem in elements:
                    text = elem.get_text()
                    href = elem.get('href', '')
                    
                    # Tìm số trong text hoặc href
                    numbers = re.findall(r'\d+', text + href)
                    if numbers:
                        page_nums = [int(n) for n in numbers if int(n) > 1 and int(n) < 1000]
                        if page_nums:
                            max_page = max(max_page, max(page_nums))
                
                print(f"    📊 Trang cao nhất tìm thấy: {max_page}")
                break
        except:
            continue
    
    return max_page

def explore_specific_categories(driver):
    """Khám phá các danh mục cụ thể"""
    print(f"\n{'='*80}")
    print(f"🏷️ KHÁM PHÁ CÁC DANH MỤC CỤ THỂ")
    print(f"{'='*80}")
    
    # Thử các URL danh mục cụ thể
    specific_categories = [
        ("Thuốc không kê đơn", "https://nhathuoclongchau.com.vn/thuoc/thuoc-khong-ke-don"),
        ("Thuốc kê đơn", "https://nhathuoclongchau.com.vn/thuoc/thuoc-ke-don"),
        ("Thuốc tim mạch", "https://nhathuoclongchau.com.vn/thuoc/thuoc-tim-mach-and-mau"),
        ("Thuốc tiêu hóa", "https://nhathuoclongchau.com.vn/thuoc/thuoc-tieu-hoa-and-gan-mat"),
        ("Thuốc thần kinh", "https://nhathuoclongchau.com.vn/thuoc/thuoc-than-kinh/thuoc-than-kinh"),
        ("Kháng sinh", "https://nhathuoclongchau.com.vn/thuoc/thuoc-khang-sinh-khang-nam"),
        ("Tất cả thuốc", "https://nhathuoclongchau.com.vn/thuoc"),
        ("Thực phẩm chức năng", "https://nhathuoclongchau.com.vn/thuc-pham-chuc-nang")
    ]
    
    total_products = set()
    
    for category_name, category_url in specific_categories:
        try:
            print(f"\n🏷️ {category_name}: {category_url}")
            driver.get(category_url)
            time.sleep(5)
            
            soup = BeautifulSoup(driver.page_source, "html.parser")
            
            # Đếm sản phẩm
            product_links = []
            all_links = soup.find_all("a", href=True)
            
            for link in all_links:
                href = link.get("href")
                if href and (".html" in href and ("thuoc" in href or "thuc-pham-chuc-nang" in href)):
                    if href.startswith("/"):
                        product_url = "https://nhathuoclongchau.com.vn" + href
                    elif href.startswith("https://nhathuoclongchau.com.vn"):
                        product_url = href
                    else:
                        continue
                    
                    # Loại bỏ query parameters
                    if "?" in product_url:
                        product_url = product_url.split("?")[0]
                    
                    if product_url not in product_links:
                        product_links.append(product_url)
            
            print(f"  📊 Tìm thấy {len(product_links)} sản phẩm")
            
            # Test pagination
            max_page = test_pagination(driver, category_url)
            if max_page > 1:
                estimated_total = len(product_links) * max_page
                print(f"  📄 Có {max_page} trang → Ước tính: {estimated_total} sản phẩm")
            
            total_products.update(product_links)
            
            # Hiển thị vài ví dụ
            if product_links:
                print(f"  📋 Ví dụ sản phẩm:")
                for i, link in enumerate(product_links[:3]):
                    print(f"    {i+1}. {link}")
            
        except Exception as e:
            print(f"  ❌ Lỗi: {e}")
    
    print(f"\n📊 TỔNG KẾT:")
    print(f"🎯 Tổng số sản phẩm unique từ tất cả danh mục: {len(total_products)}")
    
    return len(total_products)

def main():
    driver = init_browser()
    
    try:
        # 1. Test chức năng tìm kiếm
        test_search_functionality(driver)
        
        # 2. Khám phá các danh mục cụ thể
        total_products = explore_specific_categories(driver)
        
        print(f"\n{'='*80}")
        print(f"📊 KẾT LUẬN CUỐI CÙNG VỀ LONG CHÂU")
        print(f"{'='*80}")
        print(f"🎯 Ước tính tổng số sản phẩm thuốc trên Long Châu: {total_products}")
        
        if total_products < 1000:
            print(f"⚠️ Long Châu có vẻ có ít sản phẩm hơn mong đợi")
        elif total_products < 5000:
            print(f"✅ Long Châu có số lượng sản phẩm vừa phải")
        else:
            print(f"🎉 Long Châu có nhiều sản phẩm, phù hợp để crawl")
        
    except Exception as e:
        print(f"❌ Lỗi tổng quát: {e}")
    
    finally:
        driver.quit()
        print("🎉 Hoàn thành phân tích Long Châu!")

if __name__ == "__main__":
    main()
