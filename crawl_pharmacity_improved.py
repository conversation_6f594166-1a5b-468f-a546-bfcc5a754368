from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import requests

# ===== CẤU HÌNH =====
MAX_PRODUCTS = None  # Lấy tất cả sản phẩm có thể
ENABLE_SEARCH_CRAWL = True  # Bật crawl từ tìm kiếm
SEARCH_KEYWORDS = [
    "thuốc", "vitamin", "kháng sinh", "giảm đau", "cảm lạnh", 
    "tiêu hóa", "da liễu", "mắt", "tai mũi họng", "tim mạch",
    "thần kinh", "c<PERSON> xương khớp", "phụ nữ", "trẻ em", "người già"
]

# H<PERSON><PERSON> kết nối Google Sheets
def connect_to_google_sheets():
    scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
    creds = ServiceAccountCredentials.from_json_keyfile_name("medical-crawl-2024-013b6faaa588.json", scope)
    client = gspread.authorize(creds)
    sheet = client.open_by_key("1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8").worksheet("Medicine")
    return sheet

def ensure_sheet_columns(sheet, required_columns):
    """Đảm bảo sheet có đủ số cột cần thiết"""
    try:
        current_cols = sheet.col_count
        if current_cols < required_columns:
            sheet.add_cols(required_columns - current_cols)
            print(f"✅ Đã mở rộng sheet từ {current_cols} lên {required_columns} cột")
    except Exception as e:
        print(f"⚠️ Không thể mở rộng sheet: {e}")

def get_column_letter(index):
    """Chuyển đổi index cột thành tên cột (A, B, ..., Z, AA, AB, ...)"""
    if index < 26:
        return chr(ord('A') + index)
    else:
        first_letter = chr(ord('A') + (index // 26) - 1)
        second_letter = chr(ord('A') + (index % 26))
        return first_letter + second_letter

def init_browser():
    """Khởi tạo browser với cấu hình tối ưu"""
    print("🚀 Khởi tạo Chrome browser...")
    service = Service(ChromeDriverManager().install())
    
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--disable-images")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--log-level=3")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    print("✅ Chrome browser đã sẵn sàng!")
    return driver

def crawl_category_products(driver, category_url):
    """Crawl sản phẩm từ một danh mục"""
    print(f"\n🏷️ Crawl danh mục: {category_url}")
    driver.get(category_url)
    time.sleep(5)
    
    soup = BeautifulSoup(driver.page_source, "html.parser")
    
    # Sử dụng selector đúng
    product_links = []
    all_links = soup.find_all("a", href=True)
    
    for link in all_links:
        href = link.get("href")
        if href and (".html" in href or "/product/" in href):
            if href.startswith("/"):
                product_url = "https://www.pharmacity.vn" + href
            elif href.startswith("https://www.pharmacity.vn"):
                product_url = href
            else:
                continue
            
            # Loại bỏ query parameters
            if "?" in product_url:
                product_url = product_url.split("?")[0]
            
            if product_url not in product_links:
                product_links.append(product_url)
    
    print(f"  ✅ Tìm thấy {len(product_links)} sản phẩm")
    return product_links

def crawl_search_results(driver, keyword, max_pages=10):
    """Crawl sản phẩm từ kết quả tìm kiếm"""
    print(f"\n🔍 Tìm kiếm: '{keyword}'")
    
    search_url = f"https://www.pharmacity.vn/search?q={keyword}"
    driver.get(search_url)
    time.sleep(5)
    
    all_products = []
    page = 1
    
    while page <= max_pages:
        print(f"  📄 Trang {page}")
        
        soup = BeautifulSoup(driver.page_source, "html.parser")
        
        # Tìm sản phẩm trong trang hiện tại
        page_products = []
        all_links = soup.find_all("a", href=True)
        
        for link in all_links:
            href = link.get("href")
            if href and (".html" in href or "/product/" in href):
                if href.startswith("/"):
                    product_url = "https://www.pharmacity.vn" + href
                elif href.startswith("https://www.pharmacity.vn"):
                    product_url = href
                else:
                    continue
                
                # Loại bỏ query parameters
                if "?" in product_url:
                    product_url = product_url.split("?")[0]
                
                if product_url not in all_products and product_url not in page_products:
                    page_products.append(product_url)
        
        if not page_products:
            print(f"    ❌ Không tìm thấy sản phẩm mới, dừng tìm kiếm")
            break
        
        all_products.extend(page_products)
        print(f"    ✅ Tìm thấy {len(page_products)} sản phẩm mới")
        
        # Thử chuyển trang tiếp theo
        try:
            next_btn = driver.find_element(By.XPATH, "//a[contains(@class, 'next') or contains(text(), 'Tiếp') or contains(text(), 'Next')]")
            driver.execute_script("arguments[0].click();", next_btn)
            time.sleep(3)
            page += 1
        except:
            print(f"    ⚠️ Không tìm thấy nút trang tiếp theo")
            break
    
    print(f"  🎯 Tổng cộng: {len(all_products)} sản phẩm từ '{keyword}'")
    return all_products

def crawl_product_detail(driver, product_url):
    """Crawl chi tiết một sản phẩm (giữ nguyên logic cũ)"""
    # Code này giữ nguyên từ file gốc, chỉ cần import vào
    pass

def main():
    driver = init_browser()
    sheet = connect_to_google_sheets()
    ensure_sheet_columns(sheet, 50)
    
    # Tạo header
    fixed_header = ["URL", "Tên sản phẩm", "Mã sản phẩm", "Thương hiệu", "Giá", "Đơn vị", "Danh mục", "Nhà sản xuất", "Số đăng ký", "Phân loại sản phẩm", "Hoạt chất", "Chỉ định", "Đối tượng sử dụng", "Dạng bào chế", "Quy cách", "Cách dùng", "Lưu ý", "Ảnh sản phẩm", "Tổng hợp"]
    sheet.update("A1:S1", [fixed_header])
    print(f"✅ Đã tạo header với {len(fixed_header)} cột")
    
    all_product_urls = set()  # Sử dụng set để tránh trùng lặp
    
    # 1. Crawl từ các danh mục (như cũ)
    pharmacity_categories = [
        "https://www.pharmacity.vn/thuoc-khong-ke-don",
        "https://www.pharmacity.vn/thuoc-ke-don", 
        "https://www.pharmacity.vn/thuoc-khac-1",
        "https://www.pharmacity.vn/thuoc-vitamin-thuc-pham-chuc-nang",
        "https://www.pharmacity.vn/thuoc-ngua-thai",
        "https://www.pharmacity.vn/thuoc-khang-di-ung",
        "https://www.pharmacity.vn/thuoc-khang-viem",
        "https://www.pharmacity.vn/thuoc-cam-lanh",
        "https://www.pharmacity.vn/thuoc-giam-can",
        "https://www.pharmacity.vn/thuoc-mattaimui",
        "https://www.pharmacity.vn/thuoc-tieu-hoa",
        "https://www.pharmacity.vn/thuoc-danh-cho-nam",
        "https://www.pharmacity.vn/giam-dau-ha-sot",
        "https://www.pharmacity.vn/thuoc-da-lieu",
        "https://www.pharmacity.vn/thuoc-danh-cho-phu-nu",
        "https://www.pharmacity.vn/thuoc-than-kinh",
        "https://www.pharmacity.vn/thuoc-co-xuong-khop",
        "https://www.pharmacity.vn/dau-gio-dau-cu-la",
        "https://www.pharmacity.vn/thuoc-khac",
        "https://www.pharmacity.vn/vitamin-va-khoang-chat"
    ]
    
    print(f"\n{'='*80}")
    print(f"🏷️ CRAWL TỪ {len(pharmacity_categories)} DANH MỤC")
    print(f"{'='*80}")
    
    for category_url in pharmacity_categories:
        try:
            category_products = crawl_category_products(driver, category_url)
            all_product_urls.update(category_products)
            print(f"  📊 Tổng sản phẩm unique hiện tại: {len(all_product_urls)}")
        except Exception as e:
            print(f"  ❌ Lỗi crawl danh mục {category_url}: {e}")
    
    # 2. Crawl từ tìm kiếm (để tăng số lượng sản phẩm)
    if ENABLE_SEARCH_CRAWL:
        print(f"\n{'='*80}")
        print(f"🔍 CRAWL TỪ TÌM KIẾM VỚI {len(SEARCH_KEYWORDS)} TỪ KHÓA")
        print(f"{'='*80}")
        
        for keyword in SEARCH_KEYWORDS:
            try:
                search_products = crawl_search_results(driver, keyword, max_pages=5)
                all_product_urls.update(search_products)
                print(f"  📊 Tổng sản phẩm unique hiện tại: {len(all_product_urls)}")
                
                # Giới hạn nếu cần
                if MAX_PRODUCTS and len(all_product_urls) >= MAX_PRODUCTS:
                    print(f"  🎯 Đã đủ {MAX_PRODUCTS} sản phẩm, dừng tìm kiếm")
                    break
                    
            except Exception as e:
                print(f"  ❌ Lỗi tìm kiếm '{keyword}': {e}")
    
    # Chuyển set thành list
    all_product_urls = list(all_product_urls)
    
    # Giới hạn số lượng nếu cần
    if MAX_PRODUCTS and len(all_product_urls) > MAX_PRODUCTS:
        all_product_urls = all_product_urls[:MAX_PRODUCTS]
    
    print(f"\n{'='*80}")
    print(f"📊 TỔNG KẾT THU THẬP URL")
    print(f"{'='*80}")
    print(f"🎯 Tổng số sản phẩm sẽ crawl: {len(all_product_urls)}")
    
    # Crawl chi tiết từng sản phẩm (sử dụng logic từ file gốc)
    print(f"\n{'='*80}")
    print(f"🔍 BẮT ĐẦU CRAWL CHI TIẾT {len(all_product_urls)} SẢN PHẨM")
    print(f"{'='*80}")
    
    # TODO: Thêm logic crawl chi tiết từ file gốc vào đây
    
    driver.quit()
    print("🎉 Hoàn thành crawl!")

if __name__ == "__main__":
    main()
