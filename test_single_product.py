import requests
from bs4 import BeautifulSoup
import re

def test_crawl_product(url):
    """Test crawl một sản phẩm cụ thể"""
    print(f"🧪 Test crawl: {url}")

    try:
        # Gửi request
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()

        # Parse HTML
        soup = BeautifulSoup(response.content, 'html.parser')

        # Khởi tạo dữ liệu mặc định
        product_name = "Không có"
        product_code = "Không có"
        brand = "Không có"
        price = "Không có"
        category = "Không có"
        manufacturer = "Không có"
        registration_number = "Không có"
        prescription_required = "Không"
        active_ingredient = "Không có"
        indication = "Không có"
        target_user = "Không có"
        dosage_form = "Không có"
        specification = "Không có"
        usage = "Không có"

        # Lấy tên sản phẩm
        title_selectors = ['h1', '.product-title', '.product-name']
        for selector in title_selectors:
            title_element = soup.select_one(selector)
            if title_element:
                product_name = title_element.text.strip()
                break

        # Lấy mã sản phẩm
        page_text = soup.get_text()
        code_match = re.search(r'P\d{5}', page_text)
        if code_match:
            product_code = code_match.group(0)

        # Lấy thương hiệu
        brand_link = soup.find('a', href=lambda x: x and '/thuong-hieu/' in x)
        if brand_link:
            brand = brand_link.text.strip().replace("Thương hiệu: ", "")

        # Lấy giá
        price_match = re.search(r'(\d+\.?\d*)\s*₫', page_text)
        if price_match:
            price = price_match.group(0)

        # Lấy số đăng ký từ pattern cụ thể
        reg_patterns = [
            r'Số đăng ký:\s*(\d+)',
            r'(\d{12})',  # Pattern cho số đăng ký 12 chữ số
        ]
        for pattern in reg_patterns:
            reg_match = re.search(pattern, page_text)
            if reg_match:
                registration_number = reg_match.group(1) if reg_match.group(1).isdigit() else reg_match.group(0)
                break

        # Parse thông tin từ text với patterns cụ thể hơn
        # Tìm danh mục - tìm từ breadcrumb cuối trang
        if "Dầu, Cao xoa bóp" in page_text:
            category = "Dầu, Cao xoa bóp"
        elif "Thuốc không kê đơn" in page_text:
            category = "Thuốc không kê đơn"
        else:
            # Tìm từ patterns
            category_patterns = [
                r'Danh mục([^A-Z]*?)Nhà sản xuất',
                r'Danh mục([^A-Z]*?)(?=Nhà|Hoạt|Chỉ|Dạng|Quy)',
                r'Danh mục(.*?)(?=Nhà sản xuất|Hoạt chất)'
            ]
            for pattern in category_patterns:
                match = re.search(pattern, page_text)
                if match:
                    cat_text = match.group(1).strip()
                    # Lấy phần cuối cùng sau dấu "..."
                    if "..." in cat_text:
                        category = cat_text.split("...")[-1].strip()
                    else:
                        category = cat_text
                    break

        # Tìm nhà sản xuất
        manufacturer_patterns = [
            r'Nhà sản xuất([^A-Z]*?)Hoạt chất',
            r'Nhà sản xuất([^A-Z]*?)(?=Hoạt|Chỉ|Dạng|Quy)',
            r'Nhà sản xuất(.*?)(?=Hoạt chất|Chỉ định)'
        ]
        for pattern in manufacturer_patterns:
            match = re.search(pattern, page_text)
            if match:
                manufacturer = match.group(1).strip()
                break

        # Tìm hoạt chất
        active_patterns = [
            r'Hoạt chất([^A-Z]*?)Chỉ định',
            r'Hoạt chất([^A-Z]*?)(?=Chỉ|Dạng|Quy)',
            r'Hoạt chất(.*?)(?=Chỉ định|Dạng bào chế)'
        ]
        for pattern in active_patterns:
            match = re.search(pattern, page_text)
            if match:
                active_ingredient = match.group(1).strip()
                break

        # Tìm chỉ định
        indication_patterns = [
            r'Chỉ định([^A-Z]*?)Dạng bào chế',
            r'Chỉ định([^A-Z]*?)(?=Dạng|Quy)',
            r'Chỉ định(.*?)(?=Dạng bào chế|Quy cách)'
        ]
        for pattern in indication_patterns:
            match = re.search(pattern, page_text)
            if match:
                indication = match.group(1).strip()
                break

        # Tìm dạng bào chế
        dosage_patterns = [
            r'Dạng bào chế([^A-Z]*?)Quy cách',
            r'Dạng bào chế([^A-Z]*?)(?=Quy)',
            r'Dạng bào chế(.*?)(?=Quy cách|Lưu ý)'
        ]
        for pattern in dosage_patterns:
            match = re.search(pattern, page_text)
            if match:
                dosage_form = match.group(1).strip()
                break

        # Tìm quy cách
        spec_patterns = [
            r'Quy cách([^A-Z]*?)Lưu ý',
            r'Quy cách([^A-Z]*?)(?=Lưu|Đủ)',
            r'Quy cách(.*?)(?=Lưu ý|Đủ thuốc)'
        ]
        for pattern in spec_patterns:
            match = re.search(pattern, page_text)
            if match:
                specification = match.group(1).strip()
                break

        # Đối tượng sử dụng
        if "Trẻ nhỏ dưới" in page_text:
            target_user = "Người lớn và trẻ em trên 5 tuổi"
        elif "Phụ nữ có thai" in page_text and "chống chỉ định" in page_text.lower():
            target_user = "Không dành cho phụ nữ có thai"
        else:
            target_user = "Người lớn"

        # Tìm cách dùng
        usage_patterns = [
            r'Bôi.*?ngày\s*\d+\s*-\s*\d+\s*lần',
            r'\d+\s*viên.*?ngày',
            r'Liều dùng.*?ngày.*?lần',
            r'Cách dùng.*?ngày.*?lần'
        ]

        for pattern in usage_patterns:
            usage_match = re.search(pattern, page_text, re.IGNORECASE)
            if usage_match:
                usage = usage_match.group(0)
                break

        # In kết quả
        print("📋 Kết quả crawl:")
        print(f"  URL: {url}")
        print(f"  Tên sản phẩm: {product_name}")
        print(f"  Mã sản phẩm: {product_code}")
        print(f"  Thương hiệu: {brand}")
        print(f"  Giá: {price}")
        print(f"  Danh mục: {category}")
        print(f"  Nhà sản xuất: {manufacturer}")
        print(f"  Số đăng ký: {registration_number}")
        print(f"  Thuốc kê đơn: {prescription_required}")
        print(f"  Hoạt chất: {active_ingredient}")
        print(f"  Chỉ định: {indication}")
        print(f"  Đối tượng sử dụng: {target_user}")
        print(f"  Dạng bào chế: {dosage_form}")
        print(f"  Quy cách: {specification}")
        print(f"  Cách dùng: {usage}")

        return [
            url, product_name, product_code, brand, price, category,
            manufacturer, registration_number, prescription_required,
            active_ingredient, indication, target_user, dosage_form,
            specification, usage
        ]

    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return None

if __name__ == "__main__":
    # Test với sản phẩm Siang Pure Oil
    test_url = "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html"
    result = test_crawl_product(test_url)

    if result:
        print("\n✅ Test thành công!")
    else:
        print("\n❌ Test thất bại!")
