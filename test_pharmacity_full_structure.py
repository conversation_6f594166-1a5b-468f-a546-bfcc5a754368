from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import re

def init_browser():
    """Khởi tạo browser với cấu hình tối ưu"""
    print("🚀 Khởi tạo Chrome browser...")
    service = Service(ChromeDriverManager().install())
    
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--disable-images")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--log-level=3")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    print("✅ Chrome browser đã sẵn sàng!")
    return driver

def discover_main_categories(driver):
    """Khám phá tất cả category chính từ trang chủ"""
    print(f"\n{'='*80}")
    print(f"🔍 KHÁM PHÁ CÁC CATEGORY CHÍNH TỪ TRANG CHỦ PHARMACITY")
    print(f"{'='*80}")
    
    driver.get("https://www.pharmacity.vn")
    time.sleep(5)
    
    soup = BeautifulSoup(driver.page_source, "html.parser")
    
    # Tìm tất cả link có thể là category
    main_categories = []
    all_links = soup.find_all("a", href=True)
    
    # Patterns để nhận diện category
    category_patterns = [
        r'/thuoc-',
        r'/vitamin-',
        r'/duoc-my-pham',
        r'/thiet-bi-y-te',
        r'/me-va-be',
        r'/cham-soc-',
        r'/thuc-pham-chuc-nang'
    ]
    
    category_keywords = [
        'thuốc', 'vitamin', 'dược', 'mỹ phẩm', 'thiết bị y tế', 
        'mẹ và bé', 'chăm sóc', 'thực phẩm chức năng'
    ]
    
    for link in all_links:
        href = link.get("href")
        text = link.get_text(strip=True)
        
        if href and text:
            # Kiểm tra pattern URL
            is_category_url = any(pattern in href for pattern in category_patterns)
            # Kiểm tra từ khóa trong text
            is_category_text = any(keyword in text.lower() for keyword in category_keywords)
            
            if is_category_url or is_category_text:
                if href.startswith("/"):
                    full_url = "https://www.pharmacity.vn" + href
                elif href.startswith("https://www.pharmacity.vn"):
                    full_url = href
                else:
                    continue
                
                # Loại bỏ query parameters
                if "?" in full_url:
                    full_url = full_url.split("?")[0]
                
                if full_url not in [cat[1] for cat in main_categories]:
                    main_categories.append((text, full_url))
    
    print(f"📋 Tìm thấy {len(main_categories)} category chính:")
    for i, (name, url) in enumerate(main_categories):
        print(f"  {i+1}. {name}: {url}")
    
    return main_categories

def discover_subcategories(driver, main_category_url, main_category_name):
    """Khám phá tất cả subcategory từ một category chính"""
    print(f"\n🔍 Khám phá subcategories của: {main_category_name}")
    print(f"📄 URL: {main_category_url}")
    
    try:
        driver.get(main_category_url)
        time.sleep(5)
        
        soup = BeautifulSoup(driver.page_source, "html.parser")
        
        subcategories = []
        
        # Tìm các subcategory bằng nhiều cách khác nhau
        
        # Cách 1: Tìm trong navigation menu
        nav_elements = soup.find_all(['nav', 'div'], class_=lambda x: x and ('nav' in x or 'menu' in x or 'category' in x))
        
        for nav in nav_elements:
            links = nav.find_all('a', href=True)
            for link in links:
                href = link.get('href')
                text = link.get_text(strip=True)
                
                if href and text and len(text) > 3:
                    if href.startswith("/"):
                        full_url = "https://www.pharmacity.vn" + href
                    elif href.startswith("https://www.pharmacity.vn"):
                        full_url = href
                    else:
                        continue
                    
                    # Loại bỏ query parameters
                    if "?" in full_url:
                        full_url = full_url.split("?")[0]
                    
                    # Kiểm tra xem có phải subcategory không
                    if (main_category_url in full_url or 
                        any(keyword in full_url for keyword in ['thuoc', 'vitamin', 'duoc', 'cham-soc']) and
                        full_url != main_category_url):
                        
                        if full_url not in [sub[1] for sub in subcategories]:
                            subcategories.append((text, full_url))
        
        # Cách 2: Tìm tất cả link trên trang có pattern tương tự
        all_links = soup.find_all('a', href=True)
        for link in all_links:
            href = link.get('href')
            text = link.get_text(strip=True)
            
            if href and text and len(text) > 3 and len(text) < 100:
                if href.startswith("/"):
                    full_url = "https://www.pharmacity.vn" + href
                elif href.startswith("https://www.pharmacity.vn"):
                    full_url = href
                else:
                    continue
                
                # Loại bỏ query parameters
                if "?" in full_url:
                    full_url = full_url.split("?")[0]
                
                # Kiểm tra pattern subcategory
                base_path = main_category_url.replace("https://www.pharmacity.vn", "")
                if (base_path in full_url and 
                    full_url != main_category_url and
                    full_url.count('/') > base_path.count('/')):
                    
                    if full_url not in [sub[1] for sub in subcategories]:
                        subcategories.append((text, full_url))
        
        # Loại bỏ duplicate và sort
        subcategories = list(set(subcategories))
        subcategories.sort(key=lambda x: x[0])
        
        print(f"  📊 Tìm thấy {len(subcategories)} subcategories:")
        for i, (name, url) in enumerate(subcategories[:20]):  # Hiển thị tối đa 20
            print(f"    {i+1}. {name}: {url}")
        
        if len(subcategories) > 20:
            print(f"    ... và {len(subcategories) - 20} subcategories khác")
        
        return subcategories
        
    except Exception as e:
        print(f"  ❌ Lỗi khi khám phá subcategories: {e}")
        return []

def count_products_in_category(driver, category_url, category_name):
    """Đếm số sản phẩm trong một category"""
    print(f"\n📊 Đếm sản phẩm trong: {category_name}")
    
    try:
        driver.get(category_url)
        time.sleep(3)
        
        soup = BeautifulSoup(driver.page_source, "html.parser")
        
        # Tìm sản phẩm bằng selector đúng
        product_links = []
        all_links = soup.find_all("a", href=True)
        
        for link in all_links:
            href = link.get("href")
            if href and (".html" in href or "/product/" in href):
                if href.startswith("/"):
                    product_url = "https://www.pharmacity.vn" + href
                elif href.startswith("https://www.pharmacity.vn"):
                    product_url = href
                else:
                    continue
                
                # Loại bỏ query parameters
                if "?" in product_url:
                    product_url = product_url.split("?")[0]
                
                if product_url not in product_links:
                    product_links.append(product_url)
        
        print(f"  🔗 Tìm thấy {len(product_links)} sản phẩm")
        
        # Hiển thị vài ví dụ
        if product_links:
            print(f"  📋 Ví dụ sản phẩm:")
            for i, url in enumerate(product_links[:3]):
                print(f"    {i+1}. {url}")
        
        return len(product_links), product_links
        
    except Exception as e:
        print(f"  ❌ Lỗi: {e}")
        return 0, []

def main():
    driver = init_browser()
    
    try:
        # 1. Khám phá tất cả category chính
        main_categories = discover_main_categories(driver)
        
        all_categories = []
        all_product_urls = set()
        
        # 2. Với mỗi category chính, khám phá subcategories
        for main_name, main_url in main_categories[:10]:  # Giới hạn 10 category chính để test
            print(f"\n{'='*60}")
            print(f"🏷️ PHÂN TÍCH CATEGORY: {main_name}")
            print(f"{'='*60}")
            
            # Đếm sản phẩm trong category chính
            main_count, main_products = count_products_in_category(driver, main_url, main_name)
            all_product_urls.update(main_products)
            all_categories.append((main_name, main_url, main_count))
            
            # Khám phá subcategories
            subcategories = discover_subcategories(driver, main_url, main_name)
            
            # Đếm sản phẩm trong từng subcategory
            for sub_name, sub_url in subcategories[:15]:  # Giới hạn 15 subcategory mỗi category chính
                sub_count, sub_products = count_products_in_category(driver, sub_url, sub_name)
                all_product_urls.update(sub_products)
                all_categories.append((f"  └─ {sub_name}", sub_url, sub_count))
        
        # 3. Tổng kết
        print(f"\n{'='*80}")
        print(f"📊 TỔNG KẾT TOÀN BỘ PHARMACITY")
        print(f"{'='*80}")
        
        total_categories = len(all_categories)
        total_unique_products = len(all_product_urls)
        total_products_sum = sum(count for _, _, count in all_categories)
        
        print(f"🏷️ Tổng số categories đã phân tích: {total_categories}")
        print(f"🔗 Tổng số sản phẩm unique: {total_unique_products}")
        print(f"📊 Tổng số sản phẩm (có trùng lặp): {total_products_sum}")
        
        print(f"\n📋 CHI TIẾT THEO CATEGORY:")
        for name, url, count in all_categories:
            print(f"  {name}: {count} sản phẩm")
        
        print(f"\n🎯 KẾT LUẬN:")
        if total_unique_products > 1000:
            print(f"✅ Pharmacity có {total_unique_products} sản phẩm - Rất phù hợp để crawl!")
        elif total_unique_products > 500:
            print(f"✅ Pharmacity có {total_unique_products} sản phẩm - Phù hợp để crawl!")
        else:
            print(f"⚠️ Pharmacity chỉ có {total_unique_products} sản phẩm - Ít hơn mong đợi")
        
        # Lưu danh sách URL để sử dụng sau
        print(f"\n💾 Lưu danh sách {len(all_product_urls)} URL sản phẩm vào file...")
        with open("pharmacity_all_products.txt", "w", encoding="utf-8") as f:
            for url in sorted(all_product_urls):
                f.write(url + "\n")
        print(f"✅ Đã lưu vào file: pharmacity_all_products.txt")
        
    except Exception as e:
        print(f"❌ Lỗi tổng quát: {e}")
    
    finally:
        driver.quit()
        print("🎉 Hoàn thành phân tích toàn bộ cấu trúc Pharmacity!")

if __name__ == "__main__":
    main()
