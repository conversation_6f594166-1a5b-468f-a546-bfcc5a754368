from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import time
import gspread
from oauth2client.service_account import ServiceAccountCredentials

# ===== CẤU HÌNH =====
BRANDS_URL = "https://www.pharmacity.vn/thuong-hieu"
SHEET_NAME = "Brands"  # Tên sheet để ghi dữ liệu thương hiệu

def connect_to_google_sheets():
    """
    Kết nối đến Google Sheets
    """
    try:
        scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
        creds = ServiceAccountCredentials.from_json_keyfile_name("medical-crawl-2024-013b6faaa588.json", scope)
        client = gspread.authorize(creds)
        sheet = client.open_by_key("1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8").worksheet(SHEET_NAME)
        return sheet
    except Exception as e:
        print(f"❌ Lỗi kết nối Google Sheets: {e}")
        return None

def get_brands_data(driver, url):
    """
    Lấy dữ liệu thương hiệu từ trang
    """
    try:
        print(f"🔍 Đang truy cập: {url}")
        driver.get(url)
        time.sleep(5)  # Đợi trang load JavaScript
        
        soup = BeautifulSoup(driver.page_source, "html.parser")
        
        brands_data = []
        
        # Tìm tất cả các div chứa thương hiệu
        # Tìm theo cấu trúc: div > a > img + h4
        brand_containers = soup.find_all('div', class_=lambda x: x and 'm-auto' in str(x))
        
        print(f"🔍 Tìm thấy {len(brand_containers)} container có class 'm-auto'")
        
        for container in brand_containers:
            try:
                # Tìm thẻ a trong container
                link = container.find('a')
                if not link:
                    continue
                
                # Tìm thẻ img trong link
                img = link.find('img')
                if not img:
                    continue
                
                # Tìm thẻ h4 trong link
                h4 = link.find('h4')
                if not h4:
                    continue
                
                # Lấy src từ img
                img_src = img.get('src')
                if not img_src:
                    continue
                
                # Chuyển đổi URL tương đối thành tuyệt đối
                if img_src.startswith('//'):
                    img_src = 'https:' + img_src
                elif img_src.startswith('/'):
                    img_src = 'https://www.pharmacity.vn' + img_src
                
                # Lấy text từ h4
                brand_name = h4.get_text(strip=True)
                if not brand_name:
                    continue
                
                # Thêm vào danh sách
                brands_data.append({
                    'image': img_src,
                    'brand': brand_name
                })
                
                print(f"  ✅ {brand_name}: {img_src}")
                
            except Exception as e:
                print(f"  ❌ Lỗi xử lý container: {e}")
                continue
        
        print(f"✅ Tổng cộng tìm thấy {len(brands_data)} thương hiệu")
        return brands_data
        
    except Exception as e:
        print(f"❌ Lỗi khi lấy dữ liệu thương hiệu: {e}")
        return []

def write_to_sheet(sheet, brands_data):
    """
    Ghi dữ liệu thương hiệu vào Google Sheets
    """
    try:
        # Tạo header
        headers = ["Image", "Brand"]
        
        # Xóa dữ liệu cũ và ghi header
        sheet.clear()
        sheet.update("A1:B1", [headers])
        print(f"✅ Đã tạo header: {headers}")
        
        # Chuẩn bị dữ liệu để ghi
        rows_data = []
        for brand in brands_data:
            rows_data.append([brand['image'], brand['brand']])
        
        if rows_data:
            # Ghi tất cả dữ liệu cùng lúc để tránh quota exceeded
            range_to_update = f"A2:B{len(rows_data) + 1}"
            sheet.update(range_to_update, rows_data)
            print(f"✅ Đã ghi {len(rows_data)} thương hiệu vào sheet")
        else:
            print("⚠️ Không có dữ liệu để ghi")
            
    except Exception as e:
        if "quota exceeded" in str(e).lower() or "429" in str(e):
            print(f"⚠️ Quota exceeded, chờ 60 giây...")
            time.sleep(60)
            # Thử lại
            if rows_data:
                range_to_update = f"A2:B{len(rows_data) + 1}"
                sheet.update(range_to_update, rows_data)
                print(f"✅ Đã ghi (retry) {len(rows_data)} thương hiệu vào sheet")
        else:
            print(f"❌ Lỗi khi ghi vào sheet: {e}")

def main():
    """
    Hàm chính
    """
    print("🚀 Bắt đầu crawl thương hiệu Pharmacity...")
    
    # Khởi tạo Chrome
    print("🔧 Đang khởi tạo Chrome browser...")
    service = Service(ChromeDriverManager().install())
    
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_argument("--log-level=3")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    print("✅ Chrome browser đã sẵn sàng!")
    
    # Kết nối Google Sheets
    print("📊 Đang kết nối Google Sheets...")
    sheet = connect_to_google_sheets()
    
    if not sheet:
        print("❌ Không thể kết nối Google Sheets. Dừng chương trình.")
        driver.quit()
        return
    
    print(f"✅ Đã kết nối sheet '{SHEET_NAME}'!")
    
    try:
        # Lấy dữ liệu thương hiệu
        brands_data = get_brands_data(driver, BRANDS_URL)
        
        if brands_data:
            # Ghi vào Google Sheets
            write_to_sheet(sheet, brands_data)
            print(f"\n🎉 Hoàn tất! Đã crawl {len(brands_data)} thương hiệu")
        else:
            print("❌ Không lấy được dữ liệu thương hiệu nào")
        
    except Exception as e:
        print(f"❌ Lỗi trong quá trình crawl: {e}")
    
    finally:
        driver.quit()
        print("🔚 Đã đóng browser")

if __name__ == "__main__":
    main()
