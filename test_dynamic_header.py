import requests
from bs4 import BeautifulSoup
import re

def test_dynamic_header_creation(url):
    """Test tạo header động từ sản phẩm đầu tiên"""
    print(f"🧪 Test tạo header động: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        product_soup = BeautifulSoup(response.content, 'html.parser')
        
        # Khởi tạo cột từ pmc-content-html
        tong_hop = "Không có"  # Chỉ dùng cho TH1
        pmc_columns = {}  # Dictionary để lưu các cột động từ TH2
        
        # Xử lý thẻ pmc-content-html để lấy cột động
        pmc_elements = product_soup.find_all(class_="pmc-content-html")
        print(f"🔍 Tìm thấy {len(pmc_elements)} thẻ pmc-content-html")
        
        if len(pmc_elements) == 1:
            # TH1: Chỉ có 1 thẻ pmc-content-html → lấy toàn bộ vào cột "Tổng hợp"
            tong_hop = pmc_elements[0].get_text(strip=True)
            if len(tong_hop) > 1000:  # Giới hạn độ dài
                tong_hop = tong_hop[:1000] + "..."
            print(f"✅ TH1: Sẽ có cột 'Tổng hợp' với nội dung {len(tong_hop)} ký tự")

        elif len(pmc_elements) > 1:
            # TH2: Có nhiều thẻ → lấy ID thẻ cha làm tên cột, text trừ h(x) làm nội dung
            print(f"✅ TH2: Sẽ có {len(pmc_elements)} cột động")
            
            for i, element in enumerate(pmc_elements):
                print(f"\n--- Thẻ pmc-content-html {i+1} ---")
                
                # Tìm thẻ cha có ID của element pmc-content-html này
                parent_element = element.parent
                column_name = None
                
                # Tìm ID từ thẻ cha hoặc các thẻ cha phía trên
                current_element = parent_element
                while current_element and column_name is None:
                    if current_element.get('id'):
                        column_name = current_element.get('id')
                        print(f"   🏷️  Tên cột: '{column_name}'")
                        break
                    current_element = current_element.parent
                
                # Nếu không tìm thấy ID, thử lấy từ thẻ h(x) như cũ (fallback)
                if not column_name:
                    h_tags = element.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                    if h_tags:
                        column_name = h_tags[0].get_text(strip=True)
                        print(f"   🏷️  Tên cột (fallback): '{column_name}'")
                
                if column_name:
                    # Lấy toàn bộ text của element
                    full_text = element.get_text(strip=True)
                    
                    # Loại bỏ text của tất cả thẻ h(x) khỏi nội dung
                    content_text = full_text
                    h_tags = element.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                    for h_tag in h_tags:
                        h_text = h_tag.get_text(strip=True)
                        content_text = content_text.replace(h_text, "", 1)  # Chỉ replace lần đầu tiên
                    
                    # Làm sạch text
                    content_text = content_text.strip()
                    content_text = re.sub(r'\s+', ' ', content_text)  # Thay nhiều khoảng trắng bằng 1
                    
                    if len(content_text) > 1000:  # Giới hạn độ dài
                        content_text = content_text[:1000] + "..."
                    
                    # Lưu vào dictionary với ID thẻ cha làm key
                    if column_name and content_text:
                        pmc_columns[column_name] = content_text
                        print(f"   📝 Nội dung: {content_text[:50]}...")
        
        # Tạo header như trong script chính
        print(f"\n🎯 TẠO HEADER:")
        
        # Header cơ bản (18 cột đầu)
        header = ["URL", "Tên sản phẩm", "Mã sản phẩm", "Thương hiệu", "Giá", "Đơn vị", "Danh mục", "Nhà sản xuất", "Số đăng ký", "Phân loại sản phẩm", "Hoạt chất", "Chỉ định", "Đối tượng sử dụng", "Dạng bào chế", "Quy cách", "Cách dùng", "Lưu ý", "Ảnh sản phẩm"]
        print(f"📋 Header cơ bản: {len(header)} cột")
        
        # Thêm header cho các cột động (theo thứ tự alphabet)
        sorted_pmc_columns = sorted(pmc_columns.items())
        dynamic_headers = []
        for column_name, content in sorted_pmc_columns:
            header.append(column_name)
            dynamic_headers.append(column_name)
        
        if dynamic_headers:
            print(f"📋 Cột động: {dynamic_headers}")
        
        # Thêm header cho cột "Tổng hợp"
        header.append("Tổng hợp")
        print(f"📋 Cột cuối: 'Tổng hợp'")
        
        print(f"\n✅ HEADER HOÀN CHỈNH ({len(header)} cột):")
        for i, col in enumerate(header):
            print(f"   {i+1:2d}. {col}")
        
        return header, pmc_columns, tong_hop
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return [], {}, "Không có"

if __name__ == "__main__":
    # Test với sản phẩm có nhiều thẻ pmc-content-html
    test_url = "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html"
    
    print(f"{'='*80}")
    print(f"TEST TẠO HEADER ĐỘNG")
    header, pmc_cols, tong_hop = test_dynamic_header_creation(test_url)
    
    if header:
        print(f"\n🎉 Test thành công!")
        print(f"   📊 Tổng số cột: {len(header)}")
        print(f"   📊 Cột cơ bản: 18")
        print(f"   📊 Cột động: {len(pmc_cols)}")
        print(f"   📊 Cột 'Tổng hợp': 1")
        print(f"   📊 Tổng: 18 + {len(pmc_cols)} + 1 = {18 + len(pmc_cols) + 1}")
    else:
        print(f"❌ Test thất bại!")
