import requests
from bs4 import BeautifulSoup
import re

def test_unit_price_extraction(url):
    """Test việc lấy giá và đơn vị từ một sản phẩm"""
    print(f"🔍 Test lấy giá và đơn vị từ: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        page_text = soup.get_text()
        
        print(f"📄 Tìm kiếm giá trong text...")
        
        # Tìm tất cả pattern giá
        all_prices = re.findall(r'\d+\.?\d*\s*₫[^,\s]*', page_text)
        print(f"🔍 Tất cả giá tìm thấy: {all_prices}")
        
        # Test logic mới
        unit = "Không có"
        price = "Không có"
        
        # Tìm giá có đơn vị (ví dụ: "86.000 ₫/Kit")
        price_with_unit_match = re.search(r'(\d+\.?\d*)\s*₫/([^,\s]+)', page_text)
        if price_with_unit_match:
            price = price_with_unit_match.group(1) + " ₫"
            unit = price_with_unit_match.group(2)
            print(f"✅ Tìm thấy giá có đơn vị: {price} / {unit}")
        else:
            # Tìm giá thông thường
            price_match = re.search(r'(\d+\.?\d*)\s*₫', page_text)
            if price_match:
                price = price_match.group(0)
                unit = "Không có"
                print(f"✅ Tìm thấy giá thông thường: {price}")
        
        # Test tìm lưu ý
        print(f"\n📝 Tìm kiếm lưu ý...")
        notes = "Không có"
        notes_patterns = [
            r'Lưu ý([^A-Z]*?)(?=Mọi thông tin|Đọc kỹ|$)',
            r'Lưu ý(.*?)(?=Mọi thông tin|Đọc kỹ|$)',
            r'Lưu ý:\s*(.*?)(?=\n\n|\Z)',
        ]
        
        for pattern in notes_patterns:
            notes_match = re.search(pattern, page_text, re.IGNORECASE | re.DOTALL)
            if notes_match:
                notes_text = notes_match.group(1).strip()
                # Làm sạch text và giới hạn độ dài
                notes_text = re.sub(r'\s+', ' ', notes_text)
                if len(notes_text) > 200:  # Giới hạn 200 ký tự cho test
                    notes_text = notes_text[:200] + "..."
                if notes_text:
                    notes = notes_text
                    print(f"✅ Tìm thấy lưu ý: {notes}")
                    break
        
        if notes == "Không có":
            print(f"❌ Không tìm thấy lưu ý")
        
        return price, unit, notes
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return None, None, None

if __name__ == "__main__":
    # Test với sản phẩm có đơn vị Kit
    test_urls = [
        "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html",
        "https://www.pharmacity.vn/test-covid-19-antigen-rapid-test-kit-genrui-hop-20-test.html"
    ]
    
    for url in test_urls:
        print(f"\n{'='*60}")
        price, unit, notes = test_unit_price_extraction(url)
        print(f"\n📊 Kết quả:")
        print(f"   Giá: {price}")
        print(f"   Đơn vị: {unit}")
        print(f"   Lưu ý: {notes}")
