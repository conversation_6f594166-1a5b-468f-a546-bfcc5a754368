import requests
from bs4 import BeautifulSoup
import re

def test_dynamic_columns_logic(urls):
    """Test logic xử lý cột động từng sản phẩm"""
    print(f"🧪 TEST LOGIC CỘT ĐỘNG TỪNG SẢN PHẨM")
    print(f"{'='*80}")
    
    # Giả lập header cố định và dynamic_columns tracking
    fixed_header = ["URL", "Tên sản phẩm", "Mã sản phẩm", "Thương hiệu", "Gi<PERSON>", "Đơn vị", "Danh mục", "Nhà sản xuất", "Số đăng ký", "Phân loại sản phẩm", "<PERSON>ạt chất", "Chỉ định", "Đối tượng sử dụng", "Dạng bào chế", "Quy cách", "Cách dùng", "Lưu ý", "Ảnh sản phẩm", "Tổng hợp"]
    dynamic_columns = {}  # {column_name: column_index}
    
    print(f"📋 Header c<PERSON> định ({len(fixed_header)} cột): A-S")
    print(f"📋 Cột 'Tổng hợp' tại vị trí: S (cột 19)")
    print(f"📋 Các cột động sẽ bắt đầu từ: T (cột 20)")
    
    for product_num, url in enumerate(urls, 1):
        print(f"\n{'='*60}")
        print(f"🔍 SẢN PHẨM {product_num}: {url}")
        print(f"{'='*60}")
        
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            
            product_soup = BeautifulSoup(response.content, 'html.parser')
            
            # Lấy tên sản phẩm
            product_name = "Không có"
            name_selectors = [
                'h1[data-testid="product-name"]',
                'h1.product-name',
                'h1',
                '.product-title h1',
                '[data-testid="product-title"]'
            ]
            for selector in name_selectors:
                name_element = product_soup.select_one(selector)
                if name_element:
                    product_name = name_element.text.strip()
                    break
            
            print(f"📦 Tên sản phẩm: {product_name}")
            
            # Xử lý thẻ pmc-content-html để lấy cột động
            pmc_elements = product_soup.find_all(class_="pmc-content-html")
            print(f"🔍 Tìm thấy {len(pmc_elements)} thẻ pmc-content-html")
            
            pmc_columns = {}
            tong_hop = "Không có"
            
            if len(pmc_elements) == 1:
                # TH1: Chỉ có 1 thẻ pmc-content-html → lấy toàn bộ vào cột "Tổng hợp"
                tong_hop = pmc_elements[0].get_text(strip=True)
                if len(tong_hop) > 100:  # Giới hạn hiển thị
                    tong_hop = tong_hop[:100] + "..."
                print(f"✅ TH1: Cột 'Tổng hợp' = {tong_hop}")

            elif len(pmc_elements) > 1:
                # TH2: Có nhiều thẻ → lấy ID thẻ cha làm tên cột
                print(f"✅ TH2: Xử lý {len(pmc_elements)} cột động")
                
                for i, element in enumerate(pmc_elements):
                    # Tìm thẻ cha có ID
                    parent_element = element.parent
                    column_name = None
                    
                    current_element = parent_element
                    while current_element and column_name is None:
                        if current_element.get('id'):
                            column_name = current_element.get('id')
                            break
                        current_element = current_element.parent
                    
                    if column_name:
                        # Lấy text trừ thẻ h(x)
                        full_text = element.get_text(strip=True)
                        content_text = full_text
                        h_tags = element.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                        for h_tag in h_tags:
                            h_text = h_tag.get_text(strip=True)
                            content_text = content_text.replace(h_text, "", 1)
                        
                        content_text = content_text.strip()
                        content_text = re.sub(r'\s+', ' ', content_text)
                        
                        if len(content_text) > 100:  # Giới hạn hiển thị
                            content_text = content_text[:100] + "..."
                        
                        pmc_columns[column_name] = content_text
                        print(f"   📝 ID cha: '{column_name}' → {content_text}")
            
            # Giả lập logic xử lý cột động
            print(f"\n🔄 XỬ LÝ CỘT ĐỘNG:")
            
            if pmc_columns:
                print(f"   📊 Có {len(pmc_columns)} cột động cần xử lý")
                
                for column_name, content in pmc_columns.items():
                    if column_name not in dynamic_columns:
                        # Tạo cột mới
                        next_column_index = len(fixed_header) + len(dynamic_columns)  # 19 + số cột động hiện có
                        dynamic_columns[column_name] = next_column_index
                        
                        # Tính tên cột (T, U, V, ...)
                        column_letter = chr(ord('A') + next_column_index)
                        if next_column_index >= 26:
                            column_letter = 'A' + chr(ord('A') + next_column_index - 26)
                        
                        print(f"   ➕ TẠO CỘT MỚI: '{column_name}' → {column_letter} (index {next_column_index})")
                    else:
                        # Cột đã tồn tại
                        column_index = dynamic_columns[column_name]
                        column_letter = chr(ord('A') + column_index)
                        if column_index >= 26:
                            column_letter = 'A' + chr(ord('A') + column_index - 26)
                        
                        print(f"   ✅ CỘT ĐÃ CÓ: '{column_name}' → {column_letter} (index {column_index})")
            else:
                print(f"   📊 Không có cột động")
            
            print(f"\n📋 TRẠNG THÁI CỘT ĐỘNG HIỆN TẠI:")
            if dynamic_columns:
                for col_name, col_index in sorted(dynamic_columns.items(), key=lambda x: x[1]):
                    col_letter = chr(ord('A') + col_index)
                    if col_index >= 26:
                        col_letter = 'A' + chr(ord('A') + col_index - 26)
                    print(f"   {col_letter}: {col_name}")
            else:
                print(f"   (Chưa có cột động nào)")
                
        except Exception as e:
            print(f"❌ Lỗi: {str(e)}")
    
    print(f"\n{'='*80}")
    print(f"🎯 KẾT QUẢ CUỐI CÙNG:")
    print(f"   📊 Header cố định: {len(fixed_header)} cột (A-S)")
    print(f"   📊 Cột động đã tạo: {len(dynamic_columns)} cột")
    
    if dynamic_columns:
        print(f"   📋 Danh sách cột động:")
        for col_name, col_index in sorted(dynamic_columns.items(), key=lambda x: x[1]):
            col_letter = chr(ord('A') + col_index)
            if col_index >= 26:
                col_letter = 'A' + chr(ord('A') + col_index - 26)
            print(f"      {col_letter}: {col_name}")

if __name__ == "__main__":
    # Test với 2 sản phẩm khác nhau
    test_urls = [
        "https://www.pharmacity.vn/cetirizine-stada-10-mg-10vi-x-10vien.html",
        "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html"
    ]
    
    test_dynamic_columns_logic(test_urls)
