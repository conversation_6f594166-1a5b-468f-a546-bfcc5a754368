# ===== MAIN APPLICATION =====

import sys
from config.settings import get_table_config, get_available_tables, MAX_ROWS, START_ROW
from services.database_service import DatabaseService
from services.sheets_service import SheetsService
from workflows.brands_workflow import BrandsWorkflow
from workflows.single_table_workflow import SingleTableWorkflow

class DataInsertApp:
    """Main application để insert data từ Google Sheets vào MySQL"""
    
    def __init__(self, table_name):
        self.table_name = table_name
        self.config = get_table_config(table_name)
        
        # Services
        self.db_service = DatabaseService()
        self.sheets_service = SheetsService()
        
        # Workflow
        self.workflow = None
    
    def setup_workflow(self):
        """Thiết lập workflow dựa trên config"""
        workflow_type = self.config.get('workflow', 'single_table_workflow')
        connection = self.db_service.get_connection()
        
        if workflow_type == 'brands_workflow':
            self.workflow = BrandsWorkflow(connection, self.config)
        elif workflow_type == 'single_table_workflow':
            self.workflow = SingleTableWorkflow(connection, self.config)
        else:
            raise ValueError(f"Workflow '{workflow_type}' không được hỗ trợ")
    
    def run(self):
        """Chạy application"""
        try:
            self.print_header()
            
            # Kết nối services
            self.sheets_service.connect()
            self.db_service.connect()
            
            # Validate table
            if not self.db_service.validate_table(self.config['table_name']):
                sys.exit(1)
            
            # Hiển thị thông tin table hiện tại
            self.db_service.show_table_info(self.config['table_name'])
            
            # Lấy dữ liệu từ Google Sheets
            sheet_name = self.config['sheet_name']
            data_rows = self.sheets_service.get_data(sheet_name, START_ROW, MAX_ROWS)
            
            # Thiết lập và chạy workflow
            self.setup_workflow()
            self.workflow.process_data_rows(data_rows)
            
            # Hiển thị kết quả sau khi insert
            print("\n🔍 Kiểm tra lại sau khi insert:")
            self.db_service.show_table_info(self.config['table_name'])
            
            self.print_footer()
            
        except Exception as e:
            print(f"❌ Lỗi không mong muốn: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # Đóng kết nối
            self.db_service.disconnect()
    
    def print_header(self):
        """In header của application"""
        print("🚀 BẮT ĐẦU INSERT DATA TỪ GOOGLE SHEETS VÀO MYSQL")
        print("="*60)
        print(f"📊 Cấu hình: Insert tối đa {MAX_ROWS if MAX_ROWS else 'TẤT CẢ'} dòng")
        print(f"📋 Table: {self.table_name}")
        print(f"📋 Sheet: {self.config['sheet_name']}")
        print(f"📋 Workflow: {self.config.get('workflow', 'single_table_workflow')}")
        print(f"📋 Columns: {', '.join(self.config['columns'])}")
        print("="*60)
    
    def print_footer(self):
        """In footer của application"""
        print("\n" + "="*60)
        print("🎉 HOÀN TẤT INSERT DATA!")
        print("="*60)

def main():
    """Hàm main"""
    # Cấu hình table muốn chạy
    TABLE_NAME = "brands"  # Thay đổi table ở đây
    
    print(f"📋 Available tables: {', '.join(get_available_tables())}")
    print(f"🎯 Running table: {TABLE_NAME}")
    print()
    
    try:
        app = DataInsertApp(TABLE_NAME)
        app.run()
    except ValueError as e:
        print(f"❌ Lỗi cấu hình: {e}")
        print(f"💡 Available tables: {', '.join(get_available_tables())}")
    except Exception as e:
        print(f"❌ Lỗi khởi tạo application: {e}")

if __name__ == "__main__":
    main()
