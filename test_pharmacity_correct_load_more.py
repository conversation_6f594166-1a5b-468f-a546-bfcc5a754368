from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import re

def init_browser():
    """Khởi tạo browser với cấu hình tối ưu - FULL SCREEN"""
    print("🚀 Khởi tạo Chrome browser FULL SCREEN...")
    service = Service(ChromeDriverManager().install())

    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")

    # QUAN TRỌNG: <PERSON><PERSON><PERSON><PERSON> lập full screen
    chrome_options.add_argument("--start-maximized")  # Mở browser ở chế độ maximize
    chrome_options.add_argument("--window-size=1920,1080")  # Đặt kích thước cụ thể
    chrome_options.add_argument("--force-device-scale-factor=1")  # Đảm bảo scale 100%

    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--log-level=3")

    driver = webdriver.Chrome(service=service, options=chrome_options)

    # Đảm bảo browser thực sự full screen
    driver.maximize_window()

    # Kiểm tra kích thước màn hình
    window_size = driver.get_window_size()
    print(f"📐 Kích thước browser: {window_size['width']}x{window_size['height']}")

    print("✅ Chrome browser FULL SCREEN đã sẵn sàng!")
    return driver

def click_load_more_button_correct(driver, max_clicks=50):
    """Click nút Xem thêm với selector chính xác từ HTML code bạn cung cấp"""
    print("\n🔄 Bắt đầu click nút 'Xem thêm' với selector chính xác...")

    click_count = 0
    previous_product_count = 0

    # Các selector chính xác dựa trên HTML code bạn cung cấp
    load_more_selectors = [
        # Selector chính xác nhất dựa trên HTML
        "button[data-size='sm'][type='button'] span:contains('Xem thêm')",
        "button[data-size='sm'] span:contains('Xem thêm')",
        "button span:contains('Xem thêm')",

        # Selector dựa trên class
        "button.relative.flex.justify-center.outline-none.font-semibold",
        "button[class*='bg-white'][class*='border-primary-500'][class*='text-primary-500']",

        # Selector đơn giản hơn
        "//button[contains(@class, 'bg-white') and contains(@class, 'border-primary-500')]//span[text()='Xem thêm']",
        "//button[contains(@data-size, 'sm')]//span[text()='Xem thêm']",
        "//button//span[text()='Xem thêm']",
        "//div[contains(@class, 'flex justify-center')]//button//span[text()='Xem thêm']",

        # Backup selectors
        "//button[contains(text(), 'Xem thêm')]",
        "//span[text()='Xem thêm']/parent::button",
        "//div[contains(@class, 'pt-1')]//button[contains(@class, 'relative')]"
    ]

    while click_count < max_clicks:
        try:
            # Đếm sản phẩm hiện tại
            current_product_count = count_current_products(driver)
            print(f"  Lần {click_count + 1}: Hiện có {current_product_count} sản phẩm")

            # Nếu không có thay đổi và đã thử click
            if current_product_count == previous_product_count and click_count > 0:
                print(f"    ⚠️ Không có sản phẩm mới, có thể đã hết")
                break

            # Scroll xuống cuối trang
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)

            # Tìm và click nút "Xem thêm"
            button_found = False

            for i, selector in enumerate(load_more_selectors):
                try:
                    if selector.startswith("//"):
                        # XPath selector
                        button = WebDriverWait(driver, 3).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        # CSS selector
                        if ":contains" in selector:
                            # Chuyển đổi CSS contains thành XPath
                            if "span:contains('Xem thêm')" in selector:
                                xpath_selector = "//button//span[text()='Xem thêm']"
                                button = WebDriverWait(driver, 3).until(
                                    EC.element_to_be_clickable((By.XPATH, xpath_selector))
                                )
                            else:
                                continue
                        else:
                            button = WebDriverWait(driver, 3).until(
                                EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                            )

                    if button and button.is_displayed():
                        # Scroll đến nút
                        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", button)
                        time.sleep(1)

                        # Click nút
                        try:
                            button.click()
                            print(f"    ✅ Đã click nút 'Xem thêm' (selector #{i+1})")
                            button_found = True
                            time.sleep(4)  # Chờ load sản phẩm mới
                            break
                        except:
                            # Thử click bằng JavaScript
                            driver.execute_script("arguments[0].click();", button)
                            print(f"    ✅ Đã click nút 'Xem thêm' bằng JS (selector #{i+1})")
                            button_found = True
                            time.sleep(4)
                            break

                except Exception as e:
                    continue

            if not button_found:
                print(f"    🛑 Không tìm thấy nút 'Xem thêm' - có thể đã hết sản phẩm")
                break

            previous_product_count = current_product_count
            click_count += 1

        except Exception as e:
            print(f"    ❌ Lỗi khi click nút: {e}")
            break

    # Đếm sản phẩm cuối cùng
    final_count = count_current_products(driver)
    print(f"✅ Hoàn thành: {final_count} sản phẩm sau {click_count} lần click")
    return final_count

def count_current_products(driver):
    """Đếm số sản phẩm hiện tại trên trang"""
    try:
        soup = BeautifulSoup(driver.page_source, "html.parser")

        # Tìm tất cả link sản phẩm
        product_links = set()
        all_links = soup.find_all("a", href=True)

        for link in all_links:
            href = link.get("href")
            if href and ".html" in href:
                if href.startswith("/"):
                    product_url = "https://www.pharmacity.vn" + href
                elif href.startswith("https://www.pharmacity.vn"):
                    product_url = href
                else:
                    continue

                # Loại bỏ query parameters
                if "?" in product_url:
                    product_url = product_url.split("?")[0]

                # Kiểm tra xem có phải sản phẩm thực sự không
                if any(keyword in product_url for keyword in ['/thuoc-', '/thuc-pham-', '/me-va-be/', '/cham-soc-', '/thiet-bi-y-te/']):
                    product_links.add(product_url)

        return len(product_links)

    except Exception as e:
        print(f"    ❌ Lỗi đếm sản phẩm: {e}")
        return 0

def test_category_with_correct_load_more(driver, category_url, category_name):
    """Test một category với nút Xem thêm chính xác"""
    print(f"\n{'='*80}")
    print(f"🏷️ TEST CATEGORY: {category_name}")
    print(f"📄 URL: {category_url}")
    print(f"{'='*80}")

    try:
        driver.get(category_url)
        time.sleep(5)

        # Đếm sản phẩm ban đầu
        initial_count = count_current_products(driver)
        print(f"📊 Sản phẩm ban đầu: {initial_count}")

        # Click nút "Xem thêm" để load tất cả sản phẩm
        final_count = click_load_more_button_correct(driver)

        # Lấy danh sách sản phẩm cuối cùng
        soup = BeautifulSoup(driver.page_source, "html.parser")
        product_links = []
        all_links = soup.find_all("a", href=True)

        for link in all_links:
            href = link.get("href")
            if href and ".html" in href:
                if href.startswith("/"):
                    product_url = "https://www.pharmacity.vn" + href
                elif href.startswith("https://www.pharmacity.vn"):
                    product_url = href
                else:
                    continue

                if "?" in product_url:
                    product_url = product_url.split("?")[0]

                if any(keyword in product_url for keyword in ['/thuoc-', '/thuc-pham-', '/me-va-be/', '/cham-soc-', '/thiet-bi-y-te/']):
                    if product_url not in product_links:
                        product_links.append(product_url)

        print(f"\n📊 KẾT QUẢ CUỐI CÙNG:")
        print(f"  📈 Từ {initial_count} tăng lên {final_count} sản phẩm")
        print(f"  🔗 Tổng link unique: {len(product_links)}")

        # Hiển thị vài ví dụ
        if product_links:
            print(f"  📋 Ví dụ sản phẩm:")
            for i, url in enumerate(product_links[:5]):
                print(f"    {i+1}. {url}")

        return len(product_links), product_links

    except Exception as e:
        print(f"  ❌ Lỗi: {e}")
        return 0, []

def main():
    driver = init_browser()

    try:
        # Test với category thuốc tiêu hóa mà bạn đã đề cập
        test_categories = [
            ("Thuốc tiêu hóa", "https://www.pharmacity.vn/thuoc-tieu-hoa"),
            ("Thuốc cảm lạnh", "https://www.pharmacity.vn/thuoc-cam-lanh"),
            ("Giảm đau hạ sốt", "https://www.pharmacity.vn/giam-dau-ha-sot")
        ]

        all_product_urls = set()
        category_results = []

        for category_name, category_url in test_categories:
            count, products = test_category_with_correct_load_more(driver, category_url, category_name)
            all_product_urls.update(products)
            category_results.append((category_name, count, products[:5]))

        # Tổng kết
        print(f"\n{'='*80}")
        print(f"📊 TỔNG KẾT VỚI NÚT 'XEM THÊM' CHÍNH XÁC")
        print(f"{'='*80}")

        print(f"\n📋 KẾT QUẢ THEO CATEGORY:")
        total_products = 0
        for name, count, examples in category_results:
            print(f"  {name}: {count} sản phẩm")
            total_products += count

        print(f"\n📊 THỐNG KÊ:")
        print(f"🏷️ Số categories đã test: {len(test_categories)}")
        print(f"📦 Tổng sản phẩm unique: {len(all_product_urls)}")
        print(f"📈 Tổng sản phẩm (có trùng lặp): {total_products}")

        print(f"\n🎯 KẾT LUẬN:")
        if len(all_product_urls) > 1000:
            print(f"🎉 Pharmacity có {len(all_product_urls)} sản phẩm - RẤT NHIỀU!")
        elif len(all_product_urls) > 500:
            print(f"✅ Pharmacity có {len(all_product_urls)} sản phẩm - Khá nhiều!")
        else:
            print(f"⚠️ Pharmacity có {len(all_product_urls)} sản phẩm")

        # Lưu kết quả
        print(f"\n💾 Lưu danh sách {len(all_product_urls)} URL sản phẩm...")
        with open("pharmacity_correct_load_more.txt", "w", encoding="utf-8") as f:
            for url in sorted(all_product_urls):
                f.write(url + "\n")
        print(f"✅ Đã lưu vào file: pharmacity_correct_load_more.txt")

    except Exception as e:
        print(f"❌ Lỗi tổng quát: {e}")

    finally:
        driver.quit()
        print("🎉 Hoàn thành test với nút 'Xem thêm' chính xác!")

if __name__ == "__main__":
    main()
