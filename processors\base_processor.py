# ===== BASE PROCESSOR CLASS =====

from abc import ABC, abstractmethod

class BaseProcessor(ABC):
    """Base class cho tất cả data processors"""
    
    def __init__(self, config):
        self.config = config
        self.table_name = config['table_name']
        self.columns = config['columns']
        self.mapping = config['mapping']
    
    @abstractmethod
    def process_row(self, row, **kwargs):
        """Xử lý 1 dòng dữ liệu từ Google Sheets
        
        Args:
            row: Dòng dữ liệu từ Google Sheets
            **kwargs: <PERSON><PERSON><PERSON> tham số bổ sung
            
        Returns:
            dict: Dữ liệu đã xử lý hoặc None nếu không hợp lệ
        """
        pass
    
    def validate_data(self, data):
        """Kiểm tra dữ liệu có hợp lệ không"""
        return data is not None
    
    def clean_string(self, value):
        """Làm sạch chuỗi"""
        if not value:
            return ""
        return str(value).strip()
    
    def get_column_value(self, row, column_index, default=""):
        """Lấy giá trị từ cột với xử lý lỗi"""
        try:
            if column_index < len(row):
                return self.clean_string(row[column_index])
            return default
        except:
            return default
    
    def is_valid_brand_name(self, brand_name):
        """Kiểm tra tên brand có hợp lệ không"""
        if not brand_name:
            return False
        brand_name = brand_name.strip()
        if brand_name == "" or brand_name.lower() == "không có":
            return False
        return True
    
    def create_record_for_insert(self, processed_data):
        """Tạo record để insert theo thứ tự columns"""
        record = []
        for column in self.columns:
            value = processed_data.get(column, None)
            record.append(value)
        return tuple(record)
    
    def get_insert_sql(self):
        """Tạo SQL query để insert"""
        columns_str = ", ".join(self.columns)
        placeholders = ", ".join(["%s"] * len(self.columns))
        return f"INSERT INTO {self.table_name} ({columns_str}) VALUES ({placeholders})"
