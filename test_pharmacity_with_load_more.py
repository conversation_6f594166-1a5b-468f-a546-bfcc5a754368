from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import re

def init_browser():
    """Khởi tạo browser với cấu hình tối ưu"""
    print("🚀 Khởi tạo Chrome browser...")
    service = Service(ChromeDriverManager().install())
    
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--disable-images")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--log-level=3")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    print("✅ Chrome browser đã sẵn sàng!")
    return driver

def advanced_load_more_products(driver, max_attempts=50):
    """Hàm load thêm sản phẩm cải tiến - click nút Xem thêm"""
    print("\n🔄 Bắt đầu click nút 'Xem thêm' để load tất cả sản phẩm...")
    
    previous_count = 0
    attempt = 0
    no_change_count = 0
    
    while attempt < max_attempts:
        try:
            # Đếm số sản phẩm hiện tại
            current_count = 0
            
            # Thử nhiều selector để đếm sản phẩm
            product_selectors = [
                "a[href*='.html']",
                ".product-card",
                "[class*='product']",
                "div[class*='item']"
            ]
            
            best_selector = None
            for selector in product_selectors:
                try:
                    products = driver.find_elements(By.CSS_SELECTOR, selector)
                    # Lọc chỉ lấy link sản phẩm thực sự
                    real_products = []
                    for product in products:
                        href = product.get_attribute('href') if product.tag_name == 'a' else None
                        if not href:
                            link = product.find_element(By.TAG_NAME, 'a') if product.tag_name != 'a' else None
                            href = link.get_attribute('href') if link else None
                        
                        if href and '.html' in href and 'pharmacity.vn' in href:
                            real_products.append(href)
                    
                    if len(real_products) > current_count:
                        current_count = len(real_products)
                        best_selector = selector
                except:
                    continue
            
            print(f"  Lần {attempt+1}: {current_count} sản phẩm (selector: {best_selector})")
            
            # Nếu không có thay đổi
            if current_count == previous_count:
                no_change_count += 1
                
                # Thử tìm và click nút "Xem thêm"
                load_more_found = False
                
                # Các selector có thể cho nút "Xem thêm"
                load_more_selectors = [
                    "//button[contains(text(), 'Xem thêm')]",
                    "//a[contains(text(), 'Xem thêm')]",
                    "//button[contains(text(), 'Load more')]",
                    "//a[contains(text(), 'Load more')]",
                    "//button[contains(text(), 'Tải thêm')]",
                    "//a[contains(text(), 'Tải thêm')]",
                    "//button[contains(@class, 'load-more')]",
                    "//a[contains(@class, 'load-more')]",
                    "//button[contains(@class, 'btn-load')]",
                    "//a[contains(@class, 'btn-load')]",
                    "//div[contains(@class, 'load-more')]//button",
                    "//div[contains(@class, 'load-more')]//a"
                ]
                
                for selector in load_more_selectors:
                    try:
                        # Scroll xuống cuối trang trước
                        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                        time.sleep(2)
                        
                        # Tìm nút
                        load_more_btn = driver.find_element(By.XPATH, selector)
                        
                        # Kiểm tra nút có hiển thị và clickable không
                        if load_more_btn.is_displayed() and load_more_btn.is_enabled():
                            # Scroll đến nút
                            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", load_more_btn)
                            time.sleep(1)
                            
                            # Click nút
                            try:
                                load_more_btn.click()
                                print(f"    ✅ Đã click nút 'Xem thêm' (selector: {selector})")
                                load_more_found = True
                                time.sleep(4)  # Chờ load sản phẩm mới
                                break
                            except:
                                # Thử click bằng JavaScript
                                driver.execute_script("arguments[0].click();", load_more_btn)
                                print(f"    ✅ Đã click nút 'Xem thêm' bằng JS (selector: {selector})")
                                load_more_found = True
                                time.sleep(4)
                                break
                                
                    except Exception as e:
                        continue
                
                if not load_more_found:
                    print(f"    ⚠️ Không tìm thấy nút 'Xem thêm' hoặc không click được")
                    
                    # Thử scroll xuống để trigger lazy loading
                    try:
                        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                        time.sleep(3)
                        print(f"    📜 Đã scroll xuống cuối trang")
                    except:
                        pass
                
                # Nếu không có thay đổi sau 3 lần thử
                if no_change_count >= 3:
                    print(f"    🛑 Dừng sau {no_change_count} lần không có thay đổi")
                    break
            else:
                no_change_count = 0
                print(f"    📈 Tăng từ {previous_count} lên {current_count} sản phẩm")
            
            previous_count = current_count
            attempt += 1
            
        except Exception as e:
            print(f"    ❌ Lỗi khi load thêm sản phẩm: {e}")
            break
    
    print(f"✅ Hoàn thành load sản phẩm: {current_count} sản phẩm sau {attempt} lần thử")
    return current_count

def count_products_with_load_more(driver, category_url, category_name):
    """Đếm số sản phẩm trong một category với việc click nút Xem thêm"""
    print(f"\n{'='*80}")
    print(f"🏷️ PHÂN TÍCH CATEGORY: {category_name}")
    print(f"📄 URL: {category_url}")
    print(f"{'='*80}")
    
    try:
        driver.get(category_url)
        time.sleep(5)
        
        # Load tất cả sản phẩm bằng cách click nút "Xem thêm"
        total_products_count = advanced_load_more_products(driver)
        
        # Lấy HTML cuối cùng và phân tích links
        soup = BeautifulSoup(driver.page_source, "html.parser")
        
        # Tìm tất cả link sản phẩm
        product_links = []
        all_links = soup.find_all("a", href=True)
        
        for link in all_links:
            href = link.get("href")
            if href and ".html" in href:
                if href.startswith("/"):
                    product_url = "https://www.pharmacity.vn" + href
                elif href.startswith("https://www.pharmacity.vn"):
                    product_url = href
                else:
                    continue
                
                # Loại bỏ query parameters
                if "?" in product_url:
                    product_url = product_url.split("?")[0]
                
                # Kiểm tra xem có phải sản phẩm thực sự không
                if any(keyword in product_url for keyword in ['/thuoc/', '/thuc-pham-chuc-nang/', '/me-va-be/', '/cham-soc-', '/thiet-bi-y-te/']):
                    if product_url not in product_links:
                        product_links.append(product_url)
        
        print(f"\n📊 KẾT QUẢ CUỐI CÙNG:")
        print(f"  🔢 Số sản phẩm đếm được: {total_products_count}")
        print(f"  🔗 Số link sản phẩm unique: {len(product_links)}")
        
        # Hiển thị vài ví dụ
        if product_links:
            print(f"  📋 Ví dụ sản phẩm:")
            for i, url in enumerate(product_links[:5]):
                print(f"    {i+1}. {url}")
        
        return len(product_links), product_links
        
    except Exception as e:
        print(f"  ❌ Lỗi: {e}")
        return 0, []

def main():
    driver = init_browser()
    
    try:
        # Test với một vài category quan trọng
        test_categories = [
            ("Thuốc cảm lạnh", "https://www.pharmacity.vn/thuoc-cam-lanh"),
            ("Thuốc không kê đơn", "https://www.pharmacity.vn/thuoc-khong-ke-don"),
            ("Giảm đau hạ sốt", "https://www.pharmacity.vn/giam-dau-ha-sot"),
            ("Thuốc tiêu hóa", "https://www.pharmacity.vn/thuoc-tieu-hoa"),
            ("Vitamin và khoáng chất", "https://www.pharmacity.vn/vitamin-va-khoang-chat")
        ]
        
        all_product_urls = set()
        category_results = []
        
        for category_name, category_url in test_categories:
            count, products = count_products_with_load_more(driver, category_url, category_name)
            all_product_urls.update(products)
            category_results.append((category_name, count, products[:5]))
        
        # Tổng kết
        print(f"\n{'='*80}")
        print(f"📊 TỔNG KẾT SAU KHI CLICK 'XEM THÊM'")
        print(f"{'='*80}")
        
        print(f"\n📋 KẾT QUẢ THEO CATEGORY:")
        total_products = 0
        for name, count, examples in category_results:
            print(f"  {name}: {count} sản phẩm")
            total_products += count
        
        print(f"\n📊 THỐNG KÊ:")
        print(f"🏷️ Số categories đã test: {len(test_categories)}")
        print(f"📦 Tổng sản phẩm unique: {len(all_product_urls)}")
        print(f"📈 Tổng sản phẩm (có trùng lặp): {total_products}")
        
        print(f"\n🎯 KẾT LUẬN:")
        if len(all_product_urls) > 1000:
            print(f"🎉 Pharmacity có {len(all_product_urls)} sản phẩm - RẤT NHIỀU!")
        elif len(all_product_urls) > 500:
            print(f"✅ Pharmacity có {len(all_product_urls)} sản phẩm - Khá nhiều!")
        else:
            print(f"⚠️ Pharmacity có {len(all_product_urls)} sản phẩm")
        
        # Lưu kết quả
        print(f"\n💾 Lưu danh sách {len(all_product_urls)} URL sản phẩm...")
        with open("pharmacity_with_load_more.txt", "w", encoding="utf-8") as f:
            for url in sorted(all_product_urls):
                f.write(url + "\n")
        print(f"✅ Đã lưu vào file: pharmacity_with_load_more.txt")
        
        # Lưu chi tiết
        with open("pharmacity_load_more_details.txt", "w", encoding="utf-8") as f:
            f.write("PHARMACITY ANALYSIS WITH LOAD MORE BUTTON\n")
            f.write("="*50 + "\n\n")
            for name, count, examples in category_results:
                f.write(f"{name}: {count} sản phẩm\n")
                if examples:
                    f.write("Ví dụ:\n")
                    for url in examples:
                        f.write(f"  - {url}\n")
                f.write("\n")
        print(f"✅ Đã lưu chi tiết vào file: pharmacity_load_more_details.txt")
        
    except Exception as e:
        print(f"❌ Lỗi tổng quát: {e}")
    
    finally:
        driver.quit()
        print("🎉 Hoàn thành phân tích với nút 'Xem thêm'!")

if __name__ == "__main__":
    main()
