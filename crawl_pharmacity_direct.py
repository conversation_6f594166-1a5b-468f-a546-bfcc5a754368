import gspread
from oauth2client.service_account import ServiceAccountCredentials
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time
import re

# Hàm kết nối Google Sheets
def connect_to_google_sheets():
    scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
    # Sử dụng file JSON mới
    creds = ServiceAccountCredentials.from_json_keyfile_name("medical-crawl-2024-013b6faaa588.json", scope)
    client = gspread.authorize(creds)
    # Sử dụng ID của Google Sheet từ URL
    sheet = client.open_by_key("1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8").worksheet("Medicine")
    return sheet

def ensure_sheet_columns(sheet, required_columns):
    """Đ<PERSON><PERSON> bảo sheet có đủ số cột cần thiết"""
    try:
        current_cols = sheet.col_count
        if current_cols < required_columns:
            sheet.add_cols(required_columns - current_cols)
            print(f"✅ Đã mở rộng sheet từ {current_cols} lên {required_columns} cột")
    except Exception as e:
        print(f"⚠️ Không thể mở rộng sheet: {e}")

def get_column_letter(index):
    """Chuyển đổi index cột thành tên cột (A, B, ..., Z, AA, AB, ...)"""
    if index < 26:
        return chr(ord('A') + index)
    else:
        # AA, AB, AC, ... (index 26, 27, 28, ...)
        first_letter = chr(ord('A') + (index // 26) - 1)
        second_letter = chr(ord('A') + (index % 26))
        return first_letter + second_letter

# ===== DANH SÁCH URL SẢN PHẨM CỐ ĐỊNH =====
product_urls = [
    "https://www.pharmacity.vn/cetirizine-stada-10-mg-10vi-x-10vien.html",
    "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html",
    "https://www.pharmacity.vn/thuoc-nho-mat-lam-giam-cac-trieu-chung-kho-mat-systane-15ml.html",
    "https://www.pharmacity.vn/thuoc-dieu-tri-viem-loet-duong-tieu-hoa-giam-buon-non-konimag-30-goi-x-7ml-hop.html",
    "https://www.pharmacity.vn/thuoc-phong-ngua-va-dieu-tri-say-tau-xe-nautamine-hop-20-vi-x-4-vien.html",
    "https://www.pharmacity.vn/thuoc-giam-dau-ha-sot-paracetamol-stada-500mg-hop-10-vi-x-10-vien.html",
    "https://www.pharmacity.vn/thuoc-tri-viem-mui-di-ung-o-nguoi-lon-va-tre-em-tren-12-tuoi-cetirizin-10mg-hai-vi-x-10-vienhop.html",
    "https://www.pharmacity.vn/thuoc-ho-prospan-100ml-chai.html",
    "https://www.pharmacity.vn/thuoc-giam-dau-ha-sot-efferalgan-500mg-hop-16-vien-sui-bot.html",
    "https://www.pharmacity.vn/thuoc-tri-benh-reflux-da-day-thuc-quan-omeprazole-stada-20mg-hop-2-vi-x-7-vien.html"
]

print(f"🚀 CRAWL TRỰC TIẾP {len(product_urls)} SẢN PHẨM")
print("="*60)

# Khởi động trình duyệt
print("Đang khởi tạo Chrome browser...")
service = Service(ChromeDriverManager().install())

chrome_options = webdriver.ChromeOptions()
chrome_options.add_argument("--no-sandbox")
chrome_options.add_argument("--disable-dev-shm-usage")
chrome_options.add_argument("--disable-gpu")
chrome_options.add_argument("--disable-web-security")
chrome_options.add_argument("--disable-features=VizDisplayCompositor")
chrome_options.add_argument("--disable-extensions")
chrome_options.add_argument("--disable-plugins")
chrome_options.add_argument("--disable-images")
chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
chrome_options.add_experimental_option('useAutomationExtension', False)
chrome_options.add_argument("--log-level=3")

driver = webdriver.Chrome(service=service, options=chrome_options)
print("✅ Chrome browser đã sẵn sàng!")

# Kết nối Google Sheets
sheet = connect_to_google_sheets()

# Đảm bảo sheet có ít nhất 50 cột để tránh lỗi
ensure_sheet_columns(sheet, 50)

# Tạo header cố định trước (19 cột: 18 cột cơ bản + "Tổng hợp")
fixed_header = ["URL", "Tên sản phẩm", "Mã sản phẩm", "Thương hiệu", "Giá", "Đơn vị", "Danh mục", "Nhà sản xuất", "Số đăng ký", "Phân loại sản phẩm", "Hoạt chất", "Chỉ định", "Đối tượng sử dụng", "Dạng bào chế", "Quy cách", "Cách dùng", "Lưu ý", "Ảnh sản phẩm", "Tổng hợp"]
sheet.update("A1:S1", [fixed_header])
print(f"✅ Đã tạo header cố định với {len(fixed_header)} cột")

row = 2  # Bắt đầu ghi data từ hàng 2
dynamic_columns = {}  # Dictionary để track các cột động đã tạo {column_name: column_index}

# Crawl chi tiết từng sản phẩm
print(f"\n{'='*50}")
print(f"BẮT ĐẦU CRAWL CHI TIẾT {len(product_urls)} SẢN PHẨM")
print(f"{'='*50}")

for i, product_url in enumerate(product_urls):
    try:
        print(f"\n🔍 Đang crawl sản phẩm {i+1}: {product_url}")
        driver.get(product_url)
        time.sleep(3)
        product_soup = BeautifulSoup(driver.page_source, "html.parser")

        # Khởi tạo dữ liệu mặc định
        product_name = "Không có"
        product_code = "Không có"
        brand = "Không có"
        price = "Không có"
        category = "Không có"
        manufacturer = "Không có"
        registration_number = "Không có"
        product_classification = "Không có"
        active_ingredient = "Không có"
        indication = "Không có"
        target_user = "Không có"
        dosage_form = "Không có"
        specification = "Không có"
        usage = "Không có"
        product_images = "Không có"

        # Khởi tạo cột từ pmc-content-html
        tong_hop = "Không có"  # Chỉ dùng cho TH1
        pmc_columns = {}  # Dictionary để lưu các cột động từ TH2

        # Lấy tên sản phẩm
        name_selectors = [
            'h1[data-testid="product-name"]',
            'h1.product-name',
            'h1',
            '.product-title h1',
            '[data-testid="product-title"]'
        ]
        for selector in name_selectors:
            name_element = product_soup.select_one(selector)
            if name_element:
                product_name = name_element.text.strip()
                break

        # Lấy mã sản phẩm
        all_spans = product_soup.find_all('span')
        for span in all_spans:
            span_text = span.get_text(strip=True)
            if re.match(r'^P\d{5,6}$', span_text):
                product_code = span_text
                break

        # Lấy thương hiệu
        brand_link = product_soup.find('a', href=lambda x: x and '/thuong-hieu/' in x)
        if brand_link:
            brand = brand_link.text.strip().replace("Thương hiệu: ", "")

        # Lấy giá và đơn vị
        page_text = product_soup.get_text()
        unit = "Không có"
        
        price_with_unit_patterns = [
            r'(\d+[.,]?\d*)\s*₫/(Kit|Hộp|Chai|Lọ|Vỉ|Gói|Bộ|Tuýp)',
            r'(\d+[.,]?\d*)\xa0₫/(Kit|Hộp|Chai|Lọ|Vỉ|Gói|Bộ|Tuýp)',
            r'(\d+[.,]?\d*)[\s\xa0]*₫/(Kit|Hộp|Chai|Lọ|Vỉ|Gói|Bộ|Tuýp)'
        ]

        found_price_unit = False
        for pattern in price_with_unit_patterns:
            price_with_unit_match = re.search(pattern, page_text)
            if price_with_unit_match:
                price = price_with_unit_match.group(1) + " ₫"
                unit = price_with_unit_match.group(2)
                found_price_unit = True
                break

        if not found_price_unit:
            price_match = re.search(r'(\d+\.?\d*)\s*₫', page_text)
            if price_match:
                price = price_match.group(0)

        # Xử lý thẻ pmc-content-html để lấy cột động
        pmc_elements = product_soup.find_all(class_="pmc-content-html")
        print(f"   🔍 Tìm thấy {len(pmc_elements)} thẻ pmc-content-html")

        if len(pmc_elements) == 1:
            # TH1: Chỉ có 1 thẻ pmc-content-html → lấy toàn bộ vào cột "Tổng hợp"
            tong_hop = pmc_elements[0].get_text(strip=True)
            if len(tong_hop) > 1000:
                tong_hop = tong_hop[:1000] + "..."
            print(f"   ✅ TH1: Cột 'Tổng hợp'")

        elif len(pmc_elements) > 1:
            # TH2: Có nhiều thẻ → lấy ID thẻ cha làm tên cột
            print(f"   ✅ TH2: Xử lý {len(pmc_elements)} cột động")
            
            for element in pmc_elements:
                # Tìm thẻ cha có ID
                parent_element = element.parent
                column_name = None
                
                current_element = parent_element
                while current_element and column_name is None:
                    if current_element.get('id'):
                        column_name = current_element.get('id')
                        break
                    current_element = current_element.parent
                
                if column_name:
                    # Lấy text trừ thẻ h(x)
                    full_text = element.get_text(strip=True)
                    content_text = full_text
                    h_tags = element.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                    for h_tag in h_tags:
                        h_text = h_tag.get_text(strip=True)
                        content_text = content_text.replace(h_text, "", 1)
                    
                    content_text = content_text.strip()
                    content_text = re.sub(r'\s+', ' ', content_text)
                    
                    if len(content_text) > 1000:
                        content_text = content_text[:1000] + "..."
                    
                    pmc_columns[column_name] = content_text
                    print(f"      📝 ID cha: '{column_name}'")

        # Tạo data cố định (19 cột)
        base_data = [
            product_url,           # 1. URL
            product_name,          # 2. Tên sản phẩm
            product_code,          # 3. Mã sản phẩm
            brand,                 # 4. Thương hiệu
            price,                 # 5. Giá
            unit,                  # 6. Đơn vị
            category,              # 7. Danh mục
            manufacturer,          # 8. Nhà sản xuất
            registration_number,   # 9. Số đăng ký
            product_classification,# 10. Phân loại sản phẩm
            active_ingredient,     # 11. Hoạt chất
            indication,            # 12. Chỉ định
            target_user,           # 13. Đối tượng sử dụng
            dosage_form,           # 14. Dạng bào chế
            specification,         # 15. Quy cách
            usage,                 # 16. Cách dùng
            "Không có",            # 17. Lưu ý
            product_images,        # 18. Ảnh sản phẩm
            tong_hop,              # 19. Tổng hợp
        ]

        # Ghi data cố định trước (19 cột: A-S)
        sheet.update(f"A{row}:S{row}", [base_data])
        print(f"   ✅ Đã ghi data cố định: {product_name}")

        # Xử lý các cột động từ pmc-content-html
        if pmc_columns:
            print(f"   🔄 Xử lý {len(pmc_columns)} cột động...")
            
            for column_name, content in pmc_columns.items():
                # Kiểm tra xem cột này đã tồn tại chưa
                if column_name not in dynamic_columns:
                    # Tạo cột mới
                    next_column_index = len(fixed_header) + len(dynamic_columns)  # 19 + số cột động hiện có
                    dynamic_columns[column_name] = next_column_index
                    
                    # Tính tên cột (T, U, V, ...)
                    column_letter = get_column_letter(next_column_index)
                    
                    # Ghi header cho cột mới
                    sheet.update(f"{column_letter}1", [[column_name]])
                    print(f"      ➕ Tạo cột mới '{column_name}' tại {column_letter}")
                
                # Ghi data vào cột
                column_index = dynamic_columns[column_name]
                column_letter = get_column_letter(column_index)
                
                sheet.update(f"{column_letter}{row}", [[content]])
                print(f"      📝 Ghi data vào cột '{column_name}' ({column_letter})")

        time.sleep(1)  # Chờ 1 giây giữa các sản phẩm
        row += 1

    except Exception as e:
        print(f"❌ Lỗi khi crawl {product_url}: {e}")
        continue

driver.quit()
print(f"\n🎉 Hoàn tất crawl {len(product_urls)} sản phẩm!")
print(f"📊 Tổng số cột động đã tạo: {len(dynamic_columns)}")
if dynamic_columns:
    print("📋 Danh sách cột động:")
    for col_name, col_index in sorted(dynamic_columns.items(), key=lambda x: x[1]):
        col_letter = get_column_letter(col_index)
        print(f"   {col_letter}: {col_name}")
