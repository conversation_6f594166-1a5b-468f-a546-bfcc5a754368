import requests
from bs4 import BeautifulSoup
import re

def test_image_extraction(url):
    """Test việc lấy ảnh từ một sản phẩm"""
    print(f"🔍 Test lấy ảnh từ: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Lấy mã sản phẩm
        page_text = soup.get_text()
        product_code = "Không có"
        code_match = re.search(r'P\d{5}', page_text)
        if code_match:
            product_code = code_match.group(0)
        
        print(f"📋 Mã sản phẩm: {product_code}")
        
        # Lấy ảnh sản phẩm
        priority_images = []
        other_images = []
        
        all_images = soup.find_all('img')
        print(f"🖼️ Tổng số ảnh tìm thấy: {len(all_images)}")
        
        for img in all_images:
            src = img.get('src') or img.get('data-src')
            if src:
                # Chuyển đổi URL
                if src.startswith('//'):
                    src = 'https:' + src
                elif src.startswith('/'):
                    src = 'https://www.pharmacity.vn' + src
                
                # Debug: in ra tất cả ảnh
                print(f"  - {src}")
                
                # Bỏ qua ảnh không liên quan
                skip_keywords = ['banner', 'badge', 'icon', 'logo', 'frame', 'button']
                if any(keyword in src.lower() for keyword in skip_keywords):
                    print(f"    ❌ Bỏ qua (chứa từ khóa loại trừ)")
                    continue
                
                # Ưu tiên ảnh có mã sản phẩm
                if product_code != "Không có" and product_code.lower() in src.lower():
                    if src not in priority_images:
                        priority_images.append(src)
                        print(f"    ✅ Ảnh ưu tiên (có mã SP)")
                # Ảnh ecommerce khác
                elif 'ecommerce' in src.lower() and src not in other_images:
                    other_images.append(src)
                    print(f"    ⭐ Ảnh ecommerce")
        
        # Gộp ảnh
        final_images = priority_images + other_images
        final_images = final_images[:5]  # Giới hạn 5 ảnh
        
        print(f"\n📷 Kết quả cuối cùng ({len(final_images)} ảnh):")
        for i, img_url in enumerate(final_images, 1):
            print(f"  {i}. {img_url}")
        
        return final_images
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return []

if __name__ == "__main__":
    test_url = "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html"
    images = test_image_extraction(test_url)
    
    if images:
        print(f"\n✅ Thành công lấy {len(images)} ảnh!")
    else:
        print(f"\n❌ Không lấy được ảnh nào!")
