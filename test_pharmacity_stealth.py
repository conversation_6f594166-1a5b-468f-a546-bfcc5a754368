from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import time
import random

def create_stealth_driver():
    """Tạo driver với stealth mode để tránh detection"""
    print("🥷 Tạo stealth browser...")
    
    service = Service(ChromeDriverManager().install())
    
    # Stealth options
    chrome_options = webdriver.ChromeOptions()
    
    # Basic stealth
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # Disable images để test nhanh hơn
    prefs = {
        "profile.managed_default_content_settings.images": 2,
        "profile.default_content_setting_values.notifications": 2
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    # Random user agent
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    ]
    chrome_options.add_argument(f"--user-agent={random.choice(user_agents)}")
    
    # Window size randomization
    chrome_options.add_argument("--window-size=1366,768")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    # Execute stealth script
    driver.execute_script("""
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en'],
        });
        
        window.chrome = {
            runtime: {},
        };
    """)
    
    return driver

def test_pharmacity_access():
    """Test truy cập Pharmacity với stealth mode"""
    driver = None
    
    try:
        driver = create_stealth_driver()
        
        print("🌐 Đang truy cập Pharmacity...")
        driver.get("https://www.pharmacity.vn")
        
        # Random delay
        time.sleep(random.uniform(3, 6))
        
        print("📄 Kiểm tra title...")
        title = driver.title
        print(f"Title: {title}")
        
        print("🖼️ Kiểm tra images...")
        images = driver.find_elements("tag name", "img")
        print(f"Tổng số images: {len(images)}")
        
        # Kiểm tra images có src không
        loaded_images = 0
        broken_images = 0
        
        for i, img in enumerate(images[:10]):  # Chỉ check 10 images đầu
            src = img.get_attribute("src")
            alt = img.get_attribute("alt")
            
            if src and src.startswith("http"):
                loaded_images += 1
                print(f"  ✅ Image {i+1}: {alt[:30]}...")
            else:
                broken_images += 1
                print(f"  ❌ Image {i+1}: No src or broken")
        
        print(f"\n📊 Kết quả:")
        print(f"  - Images có src: {loaded_images}")
        print(f"  - Images bị lỗi: {broken_images}")
        
        # Test navigate to a product category
        print("\n🏷️ Test navigate to category...")
        driver.get("https://www.pharmacity.vn/thuoc-khong-ke-don")
        time.sleep(random.uniform(2, 4))
        
        category_title = driver.title
        print(f"Category title: {category_title}")
        
        # Check product cards
        product_cards = driver.find_elements("class name", "product-card")
        print(f"Product cards found: {len(product_cards)}")
        
        if len(product_cards) > 0:
            print("✅ Có thể truy cập category và thấy products")
        else:
            print("❌ Không thấy product cards - có thể bị chặn")
            
        # Take screenshot for debugging
        driver.save_screenshot("pharmacity_test.png")
        print("📸 Đã lưu screenshot: pharmacity_test.png")
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        if driver:
            print("🧹 Đóng browser...")
            driver.quit()

if __name__ == "__main__":
    test_pharmacity_access()
