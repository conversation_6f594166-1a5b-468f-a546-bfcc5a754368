from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import re

def init_browser():
    """Khởi tạo browser với cấu hình tối ưu"""
    print("🚀 Khởi tạo Chrome browser...")
    service = Service(ChromeDriverManager().install())
    
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--disable-images")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--log-level=3")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    print("✅ Chrome browser đã sẵn sàng!")
    return driver

def debug_product_selectors(driver, url):
    """Debug các selector có thể dùng để tìm sản phẩm"""
    print(f"\n🔍 Debug selectors cho: {url}")
    driver.get(url)
    time.sleep(5)
    
    soup = BeautifulSoup(driver.page_source, "html.parser")
    
    # Test các selector khác nhau cho Long Châu
    selectors_to_test = [
        ".product-item",
        ".product-card", 
        "[data-testid='product-card']",
        "[data-testid='product-item']",
        "a[href*='/san-pham/']",
        "a[href*='/product/']",
        "a[href*='.html']",
        ".product",
        "[class*='product']",
        "div[class*='card']",
        "div[class*='item']",
        ".item-product",
        ".product-box",
        "[class*='ProductCard']",
        "[class*='ProductItem']"
    ]
    
    print("\n📋 Kết quả test selectors:")
    best_selector = None
    max_count = 0
    
    for selector in selectors_to_test:
        try:
            elements = soup.select(selector)
            count = len(elements)
            print(f"  {selector:<35} → {count} elements")
            
            if count > max_count:
                max_count = count
                best_selector = selector
            
            # Hiển thị một vài ví dụ
            if count > 0 and count <= 50:  # Chỉ hiển thị nếu số lượng hợp lý
                for i, elem in enumerate(elements[:3]):
                    if elem.get('href'):
                        print(f"    Ví dụ {i+1}: {elem.get('href')}")
                    elif elem.find('a'):
                        link = elem.find('a')
                        if link and link.get('href'):
                            print(f"    Ví dụ {i+1}: {link.get('href')}")
        except Exception as e:
            print(f"  {selector:<35} → Lỗi: {e}")
    
    print(f"\n🎯 Selector tốt nhất: {best_selector} ({max_count} elements)")
    return best_selector, max_count

def advanced_load_more_products(driver, max_attempts=30):
    """Hàm load thêm sản phẩm cải tiến cho Long Châu"""
    print("\n🔄 Bắt đầu load thêm sản phẩm...")
    previous_count = 0
    attempt = 0
    no_change_count = 0
    
    while attempt < max_attempts:
        try:
            # Test nhiều selector khác nhau để đếm sản phẩm
            product_selectors = [
                ".product-item",
                ".product-card",
                "a[href*='/san-pham/']",
                "div[class*='product']",
                "[class*='ProductCard']"
            ]
            
            current_count = 0
            best_selector = None
            for selector in product_selectors:
                try:
                    products = driver.find_elements(By.CSS_SELECTOR, selector)
                    if len(products) > current_count:
                        current_count = len(products)
                        best_selector = selector
                except:
                    continue
            
            print(f"  Lần {attempt+1}: {current_count} sản phẩm (selector: {best_selector if current_count > 0 else 'none'})")
            
            # Nếu không có thay đổi
            if current_count == previous_count:
                no_change_count += 1
                
                # Thử các cách load thêm khác nhau cho Long Châu
                load_more_methods = [
                    # Phương pháp 1: Tìm nút "Xem thêm" tiếng Việt
                    lambda: driver.find_element(By.XPATH, "//button[contains(text(), 'Xem thêm') or contains(text(), 'Tải thêm') or contains(text(), 'Load more')]"),
                    # Phương pháp 2: Tìm nút có class chứa "load", "more", "show"
                    lambda: driver.find_element(By.CSS_SELECTOR, "button[class*='load'], button[class*='more'], button[class*='show']"),
                    # Phương pháp 3: Tìm link "Xem thêm"
                    lambda: driver.find_element(By.XPATH, "//a[contains(text(), 'Xem thêm') or contains(text(), 'Tải thêm')]"),
                    # Phương pháp 4: Scroll xuống cuối trang
                    lambda: driver.execute_script("window.scrollTo(0, document.body.scrollHeight);"),
                    # Phương pháp 5: Tìm pagination
                    lambda: driver.find_element(By.CSS_SELECTOR, ".pagination a:last-child, .pager a:last-child")
                ]
                
                success = False
                for i, method in enumerate(load_more_methods):
                    try:
                        if i < 4:  # Các phương pháp click button/link
                            element = method()
                            if i == 3:  # Scroll method
                                print(f"    ✅ Đã scroll xuống cuối trang")
                            else:
                                driver.execute_script("arguments[0].scrollIntoView();", element)
                                time.sleep(1)
                                element.click()
                                print(f"    ✅ Đã click element (phương pháp {i+1})")
                        else:  # Pagination
                            element = method()
                            driver.execute_script("arguments[0].scrollIntoView();", element)
                            time.sleep(1)
                            element.click()
                            print(f"    ✅ Đã click pagination")
                        
                        time.sleep(4)  # Chờ lâu hơn cho Long Châu
                        success = True
                        break
                    except Exception as e:
                        print(f"    ❌ Phương pháp {i+1} thất bại: {str(e)[:100]}")
                        continue
                
                if not success:
                    print(f"    ⚠️ Không thể load thêm sản phẩm")
                    
                # Nếu không có thay đổi sau 3 lần thử
                if no_change_count >= 3:
                    print(f"    🛑 Dừng sau {no_change_count} lần không có thay đổi")
                    break
            else:
                no_change_count = 0
            
            previous_count = current_count
            attempt += 1
            
        except Exception as e:
            print(f"    ❌ Lỗi khi load thêm sản phẩm: {e}")
            break
    
    print(f"✅ Hoàn thành load sản phẩm: {current_count} sản phẩm sau {attempt} lần thử")
    return current_count

def count_products_in_category(driver, category_url):
    """Đếm số sản phẩm thực tế trong một danh mục Long Châu"""
    print(f"\n{'='*80}")
    print(f"🏷️ PHÂN TÍCH DANH MỤC LONG CHÂU: {category_url}")
    print(f"{'='*80}")
    
    driver.get(category_url)
    time.sleep(5)
    
    # Debug selectors trước
    best_selector, initial_count = debug_product_selectors(driver, category_url)
    
    # Load thêm sản phẩm
    total_products = advanced_load_more_products(driver)
    
    # Lấy HTML cuối cùng và phân tích links
    soup = BeautifulSoup(driver.page_source, "html.parser")
    
    # Tìm tất cả link sản phẩm Long Châu
    product_links = []
    all_links = soup.find_all("a", href=True)
    
    for link in all_links:
        href = link.get("href")
        if href:
            # Long Châu thường có pattern /san-pham/ hoặc /product/
            if "/san-pham/" in href or "/product/" in href or ".html" in href:
                if href.startswith("/"):
                    product_url = "https://nhathuoclongchau.com.vn" + href
                elif href.startswith("https://nhathuoclongchau.com.vn"):
                    product_url = href
                elif href.startswith("https://") and "longchau" in href:
                    product_url = href
                else:
                    continue
                
                # Loại bỏ query parameters
                if "?" in product_url:
                    product_url = product_url.split("?")[0]
                
                if product_url not in product_links:
                    product_links.append(product_url)
    
    print(f"\n📊 KẾT QUẢ PHÂN TÍCH:")
    print(f"  🔢 Tổng số elements tìm thấy: {total_products}")
    print(f"  🔗 Tổng số link sản phẩm unique: {len(product_links)}")
    
    # Hiển thị một vài ví dụ link
    print(f"\n📋 VÍ DỤ LINK SẢN PHẨM (5 đầu tiên):")
    for i, link in enumerate(product_links[:5]):
        print(f"  {i+1}. {link}")
    
    return len(product_links), product_links

def explore_longchau_structure(driver):
    """Khám phá cấu trúc website Long Châu"""
    print(f"\n{'='*80}")
    print(f"🔍 KHÁM PHÁ CẤU TRÚC WEBSITE LONG CHÂU")
    print(f"{'='*80}")
    
    # Truy cập trang chủ
    driver.get("https://nhathuoclongchau.com.vn")
    time.sleep(5)
    
    soup = BeautifulSoup(driver.page_source, "html.parser")
    
    # Tìm menu danh mục
    print("\n🔍 Tìm kiếm menu danh mục...")
    
    # Tìm các link có thể là danh mục thuốc
    category_patterns = [
        r'/danh-muc/',
        r'/category/',
        r'/thuoc',
        r'/duoc-pham',
        r'/san-pham'
    ]
    
    potential_categories = []
    all_links = soup.find_all("a", href=True)
    
    for link in all_links:
        href = link.get("href")
        text = link.get_text(strip=True)
        
        if href and any(pattern in href for pattern in category_patterns):
            if href.startswith("/"):
                full_url = "https://nhathuoclongchau.com.vn" + href
            elif href.startswith("https://nhathuoclongchau.com.vn"):
                full_url = href
            else:
                continue
            
            if full_url not in [cat[0] for cat in potential_categories]:
                potential_categories.append((full_url, text))
    
    print(f"\n📋 Tìm thấy {len(potential_categories)} danh mục tiềm năng:")
    for i, (url, text) in enumerate(potential_categories[:10]):  # Hiển thị 10 đầu tiên
        print(f"  {i+1}. {text}: {url}")
    
    return potential_categories

def main():
    driver = init_browser()
    
    try:
        # Khám phá cấu trúc website trước
        categories = explore_longchau_structure(driver)
        
        # Test với một vài danh mục đầu tiên
        test_categories = [cat[0] for cat in categories[:5]]  # Lấy 5 danh mục đầu tiên
        
        if not test_categories:
            # Nếu không tìm thấy danh mục, thử với URL cố định
            test_categories = [
                "https://nhathuoclongchau.com.vn/danh-muc/thuoc-khong-ke-don",
                "https://nhathuoclongchau.com.vn/danh-muc/thuoc-ke-don",
                "https://nhathuoclongchau.com.vn/danh-muc/vitamin-va-thuc-pham-chuc-nang",
                "https://nhathuoclongchau.com.vn/san-pham",
                "https://nhathuoclongchau.com.vn/thuoc"
            ]
        
        total_unique_products = set()
        
        for category in test_categories:
            try:
                count, links = count_products_in_category(driver, category)
                total_unique_products.update(links)
                print(f"\n✅ Danh mục này có {count} sản phẩm")
                
            except Exception as e:
                print(f"❌ Lỗi khi phân tích danh mục {category}: {e}")
        
        print(f"\n{'='*80}")
        print(f"📊 TỔNG KẾT LONG CHÂU")
        print(f"{'='*80}")
        print(f"🎯 Tổng số sản phẩm unique từ {len(test_categories)} danh mục: {len(total_unique_products)}")
        
        # Thử tìm kiếm để ước tính tổng số sản phẩm
        print(f"\n🔍 Thử tìm kiếm để ước tính tổng số sản phẩm...")
        try:
            driver.get("https://nhathuoclongchau.com.vn/tim-kiem?q=thuoc")
            time.sleep(5)
            
            soup = BeautifulSoup(driver.page_source, "html.parser")
            
            # Tìm thông tin về tổng số kết quả
            result_info_selectors = [
                ".search-result-count",
                ".result-count", 
                ".total-results",
                "[class*='result']",
                "[class*='count']"
            ]
            
            for selector in result_info_selectors:
                elements = soup.select(selector)
                for elem in elements:
                    text = elem.get_text()
                    if any(keyword in text.lower() for keyword in ['kết quả', 'result', 'sản phẩm', 'product']):
                        print(f"  📊 Thông tin tìm kiếm: {text}")
                        
                        # Trích xuất số từ text
                        numbers = re.findall(r'\d+', text)
                        if numbers:
                            print(f"  🔢 Số lượng ước tính: {max(numbers)} sản phẩm")
                        
        except Exception as e:
            print(f"  ❌ Không thể ước tính từ tìm kiếm: {e}")
        
    except Exception as e:
        print(f"❌ Lỗi tổng quát: {e}")
    
    finally:
        driver.quit()
        print("🎉 Hoàn thành phân tích Long Châu!")

if __name__ == "__main__":
    main()
