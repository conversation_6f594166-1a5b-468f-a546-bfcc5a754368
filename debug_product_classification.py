import requests
from bs4 import BeautifulSoup
import re

def debug_product_classification(url):
    """Debug phân loại sản phẩm từ cấu trúc HTML"""
    print(f"🔍 Debug phân loại sản phẩm từ: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Tìm div có class "space-y-1"
        space_y_divs = soup.find_all('div', class_='space-y-1')
        print(f"📄 Tìm thấy {len(space_y_divs)} div có class 'space-y-1'")
        
        product_classification = "Không có"
        
        for i, div in enumerate(space_y_divs):
            print(f"\n--- Div space-y-1 thứ {i+1} ---")
            
            # Tìm tất cả button trong div này
            buttons = div.find_all('button')
            print(f"🔍 Tìm thấy {len(buttons)} button")
            
            for j, button in enumerate(buttons):
                button_text = button.get_text(strip=True)
                print(f"  Button {j+1}: {repr(button_text)}")
                
                # Kiểm tra nếu button chứa các từ khóa phân loại
                classification_keywords = ['Kit', 'Hộp', 'Chai', 'Lọ', 'Vỉ', 'Gói', 'Bộ', 'Tuýp', 'Viên', 'Túi']
                
                for keyword in classification_keywords:
                    if keyword.lower() in button_text.lower():
                        print(f"  ✅ Tìm thấy phân loại: {keyword}")
                        if product_classification == "Không có":
                            product_classification = keyword
                        break
            
            # Nếu đã tìm thấy phân loại, dừng lại
            if product_classification != "Không có":
                break
        
        # Nếu không tìm thấy trong space-y-1, thử tìm trong toàn bộ button
        if product_classification == "Không có":
            print(f"\n🔍 Tìm trong tất cả button...")
            all_buttons = soup.find_all('button')
            print(f"📄 Tìm thấy {len(all_buttons)} button tổng cộng")
            
            for button in all_buttons:
                button_text = button.get_text(strip=True)
                classification_keywords = ['Kit', 'Hộp', 'Chai', 'Lọ', 'Vỉ', 'Gói', 'Bộ', 'Tuýp', 'Viên', 'Túi']
                
                for keyword in classification_keywords:
                    if keyword.lower() in button_text.lower():
                        print(f"✅ Tìm thấy phân loại trong button: {keyword} (text: {repr(button_text)})")
                        product_classification = keyword
                        break
                
                if product_classification != "Không có":
                    break
        
        # Nếu vẫn không tìm thấy, thử tìm trong text tổng thể
        if product_classification == "Không có":
            print(f"\n🔍 Tìm trong text tổng thể...")
            page_text = soup.get_text()
            classification_keywords = ['Kit', 'Hộp', 'Chai', 'Lọ', 'Vỉ', 'Gói', 'Bộ', 'Tuýp', 'Viên', 'Túi']
            
            for keyword in classification_keywords:
                if keyword in page_text:
                    print(f"✅ Tìm thấy phân loại trong text: {keyword}")
                    product_classification = keyword
                    break
        
        print(f"\n📊 Kết quả phân loại sản phẩm: {product_classification}")
        return product_classification
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return "Không có"

if __name__ == "__main__":
    # Test với các sản phẩm khác nhau
    test_urls = [
        "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html",
        "https://www.pharmacity.vn/genesign-test-respiratory-combo-antigen-rapid-20-test-hop.html"
    ]
    
    for i, test_url in enumerate(test_urls):
        print(f"\n{'='*80}")
        print(f"TEST {i+1}")
        classification = debug_product_classification(test_url)
        
        print(f"\n🎯 Phân loại cuối cùng: {classification}")
