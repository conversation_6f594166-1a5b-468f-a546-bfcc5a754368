import gspread
from oauth2client.service_account import ServiceAccountCredentials

print("=== BƯỚC 1: <PERSON><PERSON><PERSON> thực ===")
try:
    scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
    creds = ServiceAccountCredentials.from_json_keyfile_name("medical-crawl-2024-013b6faaa588.json", scope)
    client = gspread.authorize(creds)
    print("✅ Xác thực thành công!")
except Exception as e:
    print(f"❌ Lỗi xác thực: {e}")
    exit()

print("\n=== BƯỚC 2: Mở Google Sheet ===")
try:
    sheet = client.open_by_key("1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8")
    print("✅ Mở Google Sheet thành công!")
    print(f"Tên sheet: {sheet.title}")
except Exception as e:
    print(f"❌ Lỗi mở sheet: {e}")
    exit()

print("\n=== BƯỚC 3: Liệt kê các worksheet ===")
try:
    worksheets = sheet.worksheets()
    print("Các worksheet có sẵn:")
    for ws in worksheets:
        print(f"  - {ws.title}")
except Exception as e:
    print(f"❌ Lỗi liệt kê worksheet: {e}")

print("\n=== BƯỚC 4: Truy cập worksheet Medicine ===")
try:
    worksheet = sheet.worksheet("Medicine")
    print("✅ Truy cập worksheet 'Medicine' thành công!")
except Exception as e:
    print(f"❌ Lỗi truy cập worksheet Medicine: {e}")
    print("→ Có thể chưa có worksheet tên 'Medicine'")
    print("→ Hãy tạo worksheet 'Medicine' trong Google Sheet")
    exit()

print("\n=== BƯỚC 5: Test ghi dữ liệu ===")
try:
    # Sử dụng format đúng cho gspread
    worksheet.update("A1:A1", [["Test connection"]])
    print("✅ Ghi dữ liệu thành công!")
    print("\n🎉 TẤT CẢ ĐỀU HOẠT ĐỘNG! Bạn có thể chạy code crawl rồi!")
except Exception as e:
    print(f"❌ Lỗi ghi dữ liệu: {e}")
    print("→ Có thể do quyền truy cập")
