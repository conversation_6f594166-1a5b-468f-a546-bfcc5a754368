import requests
from bs4 import BeautifulSoup
import re

def test_specification_extraction(url):
    """Test logic lấy quy cách mới"""
    print(f"🧪 Test lấy quy cách: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        product_soup = BeautifulSoup(response.content, 'html.parser')
        specification = "Không có"
        
        # Logic mới từ crawl_pharmacity.py
        # Phương pháp 1: Tìm thẻ p có text "Quy cách" và lấy div ngay dưới nó
        print("🔍 Phương pháp 1: Tìm thẻ p có text 'Quy cách'...")
        quy_cach_elements = product_soup.find_all('p', string=lambda text: text and 'Quy cách' in text)
        print(f"   Tìm thấy {len(quy_cach_elements)} thẻ p có text 'Quy cách'")
        
        for i, quy_cach_p in enumerate(quy_cach_elements):
            print(f"   Thẻ p {i+1}: {repr(quy_cach_p.get_text())}")
            # Tìm div ngay sau thẻ p này
            next_div = quy_cach_p.find_next_sibling('div')
            if next_div:
                spec_text = next_div.get_text(strip=True)
                print(f"   Div ngay sau: {repr(spec_text[:100])}")
                if spec_text and len(spec_text) > 3:  # Đảm bảo có nội dung thực sự
                    # Làm sạch text
                    spec_text = re.sub(r'\s+', ' ', spec_text)  # Thay nhiều khoảng trắng bằng 1
                    specification = spec_text
                    print(f"   ✅ Tìm thấy quy cách: {specification}")
                    break
            else:
                print(f"   ❌ Không tìm thấy div ngay sau thẻ p {i+1}")
        
        # Phương pháp 2: Tìm theo cấu trúc grid với text "Quy cách"
        if specification == "Không có":
            print("🔍 Phương pháp 2: Tìm trong grid...")
            grid_elements = product_soup.find_all('div', class_=lambda x: x and 'grid' in x)
            print(f"   Tìm thấy {len(grid_elements)} div có class chứa 'grid'")
            
            for i, grid in enumerate(grid_elements):
                if 'Quy cách' in grid.get_text():
                    print(f"   Grid {i+1} có chứa 'Quy cách'")
                    # Tìm div chứa nội dung quy cách trong grid này
                    content_divs = grid.find_all('div')
                    for j, div in enumerate(content_divs):
                        div_text = div.get_text(strip=True)
                        if div_text and len(div_text) > 5 and 'Quy cách' not in div_text:
                            # Kiểm tra nếu có vẻ như là quy cách (không phải số đơn thuần, không phải link)
                            if not div_text.isdigit() and 'http' not in div_text.lower():
                                print(f"     Div {j+1}: {repr(div_text[:50])}")
                                spec_text = re.sub(r'\s+', ' ', div_text)
                                specification = spec_text
                                print(f"   ✅ Tìm thấy quy cách trong grid: {specification}")
                                break
                    if specification != "Không có":
                        break
        
        # Phương pháp 3: Fallback với patterns cũ
        if specification == "Không có":
            print("🔍 Phương pháp 3: Fallback với patterns...")
            page_text = product_soup.get_text()
            spec_patterns = [
                r'Quy cách([^A-Z]*?)Lưu ý',
                r'Quy cách([^A-Z]*?)(?=Lưu|Đủ)',
                r'Quy cách(.*?)(?=Lưu ý|Đủ thuốc)'
            ]
            for pattern in spec_patterns:
                match = re.search(pattern, page_text)
                if match:
                    specification = match.group(1).strip()
                    print(f"   ✅ Tìm thấy bằng pattern: {specification}")
                    break
        
        print(f"🎯 Kết quả cuối cùng: {specification}")
        return specification
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return "Không có"

if __name__ == "__main__":
    # Test với các sản phẩm khác nhau
    test_urls = [
        "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html",
        "https://www.pharmacity.vn/genesign-test-respiratory-combo-antigen-rapid-20-test-hop.html",
    ]
    
    for i, test_url in enumerate(test_urls):
        print(f"\n{'='*80}")
        print(f"TEST {i+1}")
        specification = test_specification_extraction(test_url)
        print(f"🎯 Quy cách cuối cùng: {specification}")
