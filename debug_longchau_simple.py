from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time
import requests

def init_browser():
    """Khởi tạo browser với cấu hình tối ưu"""
    print("🚀 Khởi tạo Chrome browser...")
    service = Service(ChromeDriverManager().install())
    
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--log-level=3")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    print("✅ Chrome browser đã sẵn sàng!")
    return driver

def test_with_requests():
    """Test với requests để xem có thể truy cập được không"""
    print(f"\n{'='*80}")
    print(f"🌐 TEST TRUY CẬP BẰNG REQUESTS")
    print(f"{'='*80}")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    test_urls = [
        "https://nhathuoclongchau.com.vn",
        "https://www.nhathuoclongchau.com.vn",
        "https://nhathuoclongchau.com",
        "https://longchau.com.vn"
    ]
    
    for url in test_urls:
        try:
            print(f"\n🔍 Test URL: {url}")
            response = requests.get(url, headers=headers, timeout=10)
            print(f"  📊 Status code: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                title = soup.find('title')
                if title:
                    print(f"  📄 Title: {title.get_text()}")
                
                # Tìm link sản phẩm
                links = soup.find_all('a', href=True)
                product_links = []
                
                for link in links[:50]:  # Chỉ check 50 link đầu
                    href = link.get('href')
                    if href and ('.html' in href or '/thuoc/' in href or '/san-pham/' in href):
                        if href.startswith('/'):
                            full_url = url + href
                        else:
                            full_url = href
                        
                        if full_url not in product_links:
                            product_links.append(full_url)
                
                print(f"  🔗 Tìm thấy {len(product_links)} link sản phẩm tiềm năng")
                
                if product_links:
                    print(f"  📋 Ví dụ:")
                    for i, link in enumerate(product_links[:3]):
                        print(f"    {i+1}. {link}")
                
                return url  # Trả về URL hoạt động
                
        except Exception as e:
            print(f"  ❌ Lỗi: {e}")
    
    return None

def test_homepage_with_selenium(driver, working_url):
    """Test trang chủ với Selenium"""
    print(f"\n{'='*80}")
    print(f"🔍 TEST TRANG CHỦ VỚI SELENIUM")
    print(f"{'='*80}")
    
    try:
        print(f"📄 Truy cập: {working_url}")
        driver.get(working_url)
        time.sleep(10)  # Chờ lâu hơn
        
        # Lấy title
        title = driver.title
        print(f"📄 Title: {title}")
        
        # Lấy HTML
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        
        # Tìm menu navigation
        print(f"\n🔍 Tìm menu navigation...")
        nav_selectors = ['nav', '.nav', '.menu', '.navigation', '[class*="nav"]', '[class*="menu"]']
        
        for selector in nav_selectors:
            elements = soup.select(selector)
            if elements:
                print(f"  ✅ Tìm thấy {len(elements)} element với selector: {selector}")
                
                # Lấy text từ navigation
                for i, elem in enumerate(elements[:3]):
                    nav_text = elem.get_text(strip=True)
                    if len(nav_text) > 10 and len(nav_text) < 500:
                        print(f"    Nav {i+1}: {nav_text[:100]}...")
        
        # Tìm link danh mục
        print(f"\n🔍 Tìm link danh mục...")
        all_links = soup.find_all('a', href=True)
        category_links = []
        
        for link in all_links:
            href = link.get('href')
            text = link.get_text(strip=True)
            
            if href and text:
                # Tìm link có từ khóa liên quan đến thuốc
                keywords = ['thuốc', 'medicine', 'drug', 'pharmacy', 'danh mục', 'category']
                if any(keyword in text.lower() or keyword in href.lower() for keyword in keywords):
                    if href.startswith('/'):
                        full_url = working_url + href
                    else:
                        full_url = href
                    
                    category_links.append((text, full_url))
        
        print(f"  📋 Tìm thấy {len(category_links)} link danh mục tiềm năng:")
        for i, (text, url) in enumerate(category_links[:10]):
            print(f"    {i+1}. {text}: {url}")
        
        # Tìm sản phẩm trên trang chủ
        print(f"\n🔍 Tìm sản phẩm trên trang chủ...")
        product_selectors = [
            '.product', '.product-card', '.product-item',
            '[class*="product"]', '[class*="item"]',
            'a[href*=".html"]', 'a[href*="/thuoc/"]'
        ]
        
        for selector in product_selectors:
            elements = soup.select(selector)
            if elements and len(elements) < 100:  # Tránh selector quá chung
                print(f"  📊 Selector '{selector}': {len(elements)} elements")
                
                # Hiển thị vài ví dụ
                for i, elem in enumerate(elements[:3]):
                    if elem.get('href'):
                        print(f"    {i+1}. {elem.get('href')}")
                    elif elem.find('a'):
                        link = elem.find('a')
                        if link and link.get('href'):
                            print(f"    {i+1}. {link.get('href')}")
        
        return category_links
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return []

def test_specific_product_page(driver, working_url):
    """Test một trang sản phẩm cụ thể"""
    print(f"\n{'='*80}")
    print(f"🔍 TEST TRANG SẢN PHẨM CỤ THỂ")
    print(f"{'='*80}")
    
    # Thử một số URL sản phẩm có thể có
    test_product_urls = [
        f"{working_url}/thuoc/paracetamol-500mg.html",
        f"{working_url}/san-pham/paracetamol.html",
        f"{working_url}/thuoc/paracetamol",
        f"{working_url}/product/paracetamol"
    ]
    
    for url in test_product_urls:
        try:
            print(f"\n📄 Test URL: {url}")
            driver.get(url)
            time.sleep(5)
            
            if "404" not in driver.title.lower() and "not found" not in driver.title.lower():
                print(f"  ✅ Trang tồn tại: {driver.title}")
                
                soup = BeautifulSoup(driver.page_source, 'html.parser')
                
                # Tìm thông tin sản phẩm
                product_info = soup.find('h1')
                if product_info:
                    print(f"  📋 Tên sản phẩm: {product_info.get_text()}")
                
                return True
            else:
                print(f"  ❌ Trang không tồn tại")
                
        except Exception as e:
            print(f"  ❌ Lỗi: {e}")
    
    return False

def main():
    # 1. Test với requests trước
    working_url = test_with_requests()
    
    if not working_url:
        print("❌ Không thể truy cập Long Châu bằng requests")
        return
    
    # 2. Test với Selenium
    driver = init_browser()
    
    try:
        # Test trang chủ
        category_links = test_homepage_with_selenium(driver, working_url)
        
        # Test trang sản phẩm
        test_specific_product_page(driver, working_url)
        
        print(f"\n{'='*80}")
        print(f"📊 KẾT LUẬN")
        print(f"{'='*80}")
        
        if category_links:
            print(f"✅ Long Châu có thể truy cập được")
            print(f"📋 Tìm thấy {len(category_links)} danh mục tiềm năng")
            print(f"🎯 Có thể crawl được sản phẩm từ website này")
        else:
            print(f"⚠️ Long Châu có thể truy cập nhưng cấu trúc phức tạp")
            print(f"🔧 Cần phân tích sâu hơn để tìm cách crawl")
        
    except Exception as e:
        print(f"❌ Lỗi tổng quát: {e}")
    
    finally:
        driver.quit()
        print("🎉 Hoàn thành phân tích!")

if __name__ == "__main__":
    main()
