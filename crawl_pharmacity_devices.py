from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import re

# ===== CẤU HÌNH =====
START_URL = "https://www.pharmacity.vn/thiet-bi-y-te-2"
SHEET_NAME = "MedicalDevice"  # Tên sheet cho thiết bị y tế
MAX_PRODUCTS = 1000  # Số sản phẩm tối đa muốn crawl

# Hàm kết nối Google Sheets
def connect_to_google_sheets():
    scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
    creds = ServiceAccountCredentials.from_json_keyfile_name("medical-crawl-2024-013b6faaa588.json", scope)
    client = gspread.authorize(creds)
    sheet = client.open_by_key("1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8").worksheet(SHEET_NAME)
    return sheet

def get_product_links(driver, url):
    """
    Lấy danh sách link sản phẩm từ trang danh mục
    """
    try:
        print(f"🔍 Đang truy cập: {url}")
        driver.get(url)
        time.sleep(5)  # Đợi trang load JavaScript
        
        soup = BeautifulSoup(driver.page_source, "html.parser")
        
        # Tìm tất cả link sản phẩm (kết thúc bằng .html)
        product_links = []
        links = soup.find_all('a', href=True)

        for link in links:
            href = link['href']
            # Chuyển đổi URL tương đối thành tuyệt đối
            if href.startswith('/'):
                href = 'https://www.pharmacity.vn' + href

            if href.endswith('.html') and 'pharmacity.vn' in href:
                if href not in product_links:
                    product_links.append(href)
                    print(f"  📦 Tìm thấy: {href}")
        
        print(f"✅ Tìm thấy {len(product_links)} sản phẩm")
        return product_links
        
    except Exception as e:
        print(f"❌ Lỗi khi lấy danh sách sản phẩm: {e}")
        return []

def click_load_more(driver):
    """
    Click nút "Xem thêm" để load thêm sản phẩm
    """
    try:
        # Tìm nút "Xem thêm"
        buttons = driver.find_elements(By.TAG_NAME, "button")
        
        for button in buttons:
            try:
                button_text = button.text.strip().lower()
                if 'xem thêm' in button_text:
                    print("🔘 Tìm thấy nút 'Xem thêm', đang click...")
                    
                    # Scroll đến nút
                    driver.execute_script("arguments[0].scrollIntoView(true);", button)
                    time.sleep(1)
                    
                    # Click nút
                    button.click()
                    time.sleep(3)  # Đợi load thêm sản phẩm
                    
                    print("✅ Đã click nút 'Xem thêm'")
                    return True
            except:
                continue
        
        print("❌ Không tìm thấy nút 'Xem thêm'")
        return False
        
    except Exception as e:
        print(f"❌ Lỗi khi click nút 'Xem thêm': {e}")
        return False

def get_product_details(driver, url):
    """
    Lấy thông tin chi tiết sản phẩm từ URL
    """
    try:
        print(f"🔍 Đang crawl: {url}")
        driver.get(url)
        time.sleep(3)
        
        soup = BeautifulSoup(driver.page_source, "html.parser")
        
        # Khởi tạo dữ liệu sản phẩm
        product_data = {
            'name': '',
            'price': '',
            'original_price': '',
            'discount': '',
            'rating': '',
            'review_count': '',
            'brand': '',
            'category': '',
            'description': '',
            'ingredients': '',
            'usage': '',
            'dosage': '',
            'side_effects': '',
            'contraindications': '',
            'storage': '',
            'manufacturer': '',
            'country': '',
            'images': '',
            'url': url
        }
        
        # 1. Tên sản phẩm
        name_selectors = [
            'h1[data-testid="product-name"]',
            'h1.product-name',
            'h1',
            '.product-title h1'
        ]
        
        for selector in name_selectors:
            try:
                element = soup.select_one(selector)
                if element and element.get_text(strip=True):
                    product_data['name'] = element.get_text(strip=True)
                    break
            except:
                continue
        
        # 2. Giá sản phẩm
        price_selectors = [
            '[data-testid="product-price"]',
            '.price-current',
            '.product-price',
            '.price'
        ]
        
        for selector in price_selectors:
            try:
                element = soup.select_one(selector)
                if element:
                    price_text = element.get_text(strip=True)
                    # Lấy số từ text giá
                    price_match = re.search(r'[\d,]+', price_text.replace('.', ','))
                    if price_match:
                        product_data['price'] = price_match.group()
                        break
            except:
                continue
        
        # 3. Mô tả sản phẩm
        desc_selectors = [
            '[data-testid="product-description"]',
            '.product-description',
            '.description',
            '.product-detail'
        ]
        
        for selector in desc_selectors:
            try:
                element = soup.select_one(selector)
                if element:
                    product_data['description'] = element.get_text(strip=True)[:500]  # Giới hạn 500 ký tự
                    break
            except:
                continue
        
        # 4. Ảnh sản phẩm
        image_urls = []
        
        # Tìm ảnh trong swiper
        swiper_images = soup.find_all('img')
        for img in swiper_images:
            src = img.get('src') or img.get('data-src')
            if src:
                # Chuyển đổi URL tương đối thành tuyệt đối
                if src.startswith('//'):
                    src = 'https:' + src
                elif src.startswith('/'):
                    src = 'https://www.pharmacity.vn' + src
                
                # Kiểm tra pattern ảnh sản phẩm
                if re.search(r'P\d{5}_\d+\.(png|jpg|jpeg|webp)', src, re.IGNORECASE):
                    if 'ecommerce' in src.lower() and src not in image_urls:
                        image_urls.append(src)
        
        # Nếu không tìm thấy ảnh theo pattern, lấy ảnh từ swiper-slide
        if not image_urls:
            swiper_slides = soup.find_all('div', class_=lambda x: x and 'swiper-slide' in str(x))
            for slide in swiper_slides:
                images = slide.find_all('img')
                for img in images:
                    src = img.get('src') or img.get('data-src')
                    if src:
                        if src.startswith('//'):
                            src = 'https:' + src
                        elif src.startswith('/'):
                            src = 'https://www.pharmacity.vn' + src
                        
                        skip_keywords = ['banner', 'badge', 'icon', 'logo', 'frame', 'button']
                        if not any(keyword in src.lower() for keyword in skip_keywords):
                            if src not in image_urls:
                                image_urls.append(src)
        
        # Giới hạn 5 ảnh
        image_urls = image_urls[:5]
        product_data['images'] = '\n'.join(image_urls) if image_urls else 'Không có ảnh'
        
        print(f"✅ Crawl thành công: {product_data['name'][:50]}...")
        return product_data
        
    except Exception as e:
        print(f"❌ Lỗi khi crawl {url}: {e}")
        return None

def main():
    # Khởi tạo Chrome
    print("🚀 Đang khởi tạo Chrome browser...")
    service = Service(ChromeDriverManager().install())
    
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_argument("--log-level=3")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    print("✅ Chrome browser đã sẵn sàng!")
    
    # Kết nối Google Sheets
    print("📊 Đang kết nối Google Sheets...")
    sheet = connect_to_google_sheets()
    print("✅ Đã kết nối Google Sheets!")
    
    try:
        # Tạo header cho sheet nếu chưa có
        headers = [
            'STT', 'Tên sản phẩm', 'Giá', 'Giá gốc', 'Giảm giá', 'Đánh giá', 'Số lượt đánh giá',
            'Thương hiệu', 'Danh mục', 'Mô tả', 'Thành phần', 'Cách dùng', 'Liều dùng',
            'Tác dụng phụ', 'Chống chỉ định', 'Bảo quản', 'Nhà sản xuất', 'Xuất xứ', 'Ảnh', 'URL'
        ]
        
        # Kiểm tra xem đã có header chưa
        try:
            first_row = sheet.row_values(1)
            if not first_row or len(first_row) < len(headers):
                sheet.insert_row(headers, 1)
                print("✅ Đã tạo header cho sheet")
        except:
            sheet.insert_row(headers, 1)
            print("✅ Đã tạo header cho sheet")
        
        # Bắt đầu crawl
        all_products = []
        page_count = 0
        
        # Load trang đầu tiên
        current_url = START_URL
        
        while len(all_products) < MAX_PRODUCTS:
            page_count += 1
            print(f"\n📄 Đang crawl trang {page_count}...")
            
            # Lấy danh sách sản phẩm từ trang hiện tại
            product_links = get_product_links(driver, current_url)
            
            if not product_links:
                print("❌ Không tìm thấy sản phẩm nào")
                break
            
            # Crawl từng sản phẩm
            for i, product_url in enumerate(product_links):
                if len(all_products) >= MAX_PRODUCTS:
                    break
                
                print(f"\n📦 Sản phẩm {len(all_products) + 1}/{MAX_PRODUCTS}")
                product_data = get_product_details(driver, product_url)
                
                if product_data:
                    all_products.append(product_data)
                    
                    # Ghi vào Google Sheets
                    row_data = [
                        len(all_products),  # STT
                        product_data['name'],
                        product_data['price'],
                        product_data['original_price'],
                        product_data['discount'],
                        product_data['rating'],
                        product_data['review_count'],
                        product_data['brand'],
                        product_data['category'],
                        product_data['description'],
                        product_data['ingredients'],
                        product_data['usage'],
                        product_data['dosage'],
                        product_data['side_effects'],
                        product_data['contraindications'],
                        product_data['storage'],
                        product_data['manufacturer'],
                        product_data['country'],
                        product_data['images'],
                        product_data['url']
                    ]
                    
                    sheet.append_row(row_data)
                    print(f"✅ Đã ghi sản phẩm {len(all_products)} vào sheet")
                    
                    time.sleep(1)  # Tránh spam Google Sheets API
                
                time.sleep(2)  # Tránh bị block
            
            # Thử click "Xem thêm" để load thêm sản phẩm
            driver.get(current_url)  # Quay lại trang danh mục
            time.sleep(3)
            
            if not click_load_more(driver):
                print("❌ Không thể load thêm sản phẩm")
                break
        
        print(f"\n🎉 Hoàn tất! Đã crawl {len(all_products)} sản phẩm thiết bị y tế")
        
    except Exception as e:
        print(f"❌ Lỗi trong quá trình crawl: {e}")
    
    finally:
        driver.quit()
        print("🔚 Đã đóng browser")

if __name__ == "__main__":
    main()
