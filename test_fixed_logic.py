from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def init_browser():
    """Khởi tạo browser full screen"""
    print("🚀 Khởi tạo Chrome browser FULL SCREEN...")
    service = Service(ChromeDriverManager().install())
    
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--force-device-scale-factor=1")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--log-level=3")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.maximize_window()
    
    window_size = driver.get_window_size()
    print(f"📐 Kích thước browser: {window_size['width']}x{window_size['height']}")
    print("✅ Chrome browser FULL SCREEN đã sẵn sàng!")
    return driver

def click_load_more_until_gone(driver):
    """Click nút Xem thêm liên tục cho đến khi nút biến mất"""
    print("\n🔄 Click 'Xem thêm' liên tục cho đến khi hết...")
    
    click_count = 0
    
    while True:
        try:
            # Đếm số product-card hiện tại
            product_cards = driver.find_elements(By.CLASS_NAME, "product-card")
            current_count = len(product_cards)
            print(f"  Lần {click_count + 1}: {current_count} product-card")
            
            # Scroll xuống cuối trang
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            
            # Tìm nút "Xem thêm" với selector chính xác
            try:
                # Thử tìm nút bằng XPath
                load_more_btn = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, "//button//span[text()='Xem thêm']"))
                )
                
                if load_more_btn and load_more_btn.is_displayed():
                    # Scroll đến nút
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", load_more_btn)
                    time.sleep(1)
                    
                    # Click nút
                    try:
                        load_more_btn.click()
                        print(f"    ✅ Đã click 'Xem thêm'")
                        click_count += 1
                        time.sleep(4)  # Chờ load sản phẩm mới
                    except:
                        # Thử click bằng JavaScript
                        driver.execute_script("arguments[0].click();", load_more_btn)
                        print(f"    ✅ Đã click 'Xem thêm' bằng JS")
                        click_count += 1
                        time.sleep(4)
                else:
                    print(f"    🛑 Nút 'Xem thêm' không hiển thị - Đã hết sản phẩm")
                    break
                    
            except TimeoutException:
                print(f"    🛑 Không tìm thấy nút 'Xem thêm' - Đã hết sản phẩm")
                break
            except Exception as e:
                print(f"    🛑 Lỗi khi tìm nút 'Xem thêm': {e}")
                break
                
        except Exception as e:
            print(f"    ❌ Lỗi: {e}")
            break
    
    # Đếm số product-card cuối cùng
    final_product_cards = driver.find_elements(By.CLASS_NAME, "product-card")
    final_count = len(final_product_cards)
    
    print(f"✅ Hoàn thành sau {click_count} lần click")
    print(f"📊 Tổng số product-card: {final_count}")
    
    return final_count

def count_all_html_links(driver):
    """Đếm TẤT CẢ link .html (logic mới - không lọc)"""
    soup = BeautifulSoup(driver.page_source, "html.parser")
    
    # Tìm TẤT CẢ link .html trên trang (không lọc theo từ khóa)
    product_links = soup.find_all("a", href=True)

    category_products = []
    for link in product_links:
        href = link.get("href")
        if href and ".html" in href:
            if href.startswith("/"):
                product_url = "https://www.pharmacity.vn" + href
            elif href.startswith("https://www.pharmacity.vn"):
                product_url = href
            else:
                continue

            # Loại bỏ query parameters
            if "?" in product_url:
                product_url = product_url.split("?")[0]

            # Lấy TẤT CẢ link .html (không lọc theo từ khóa nữa)
            if product_url not in category_products:
                category_products.append(product_url)
    
    return len(category_products), category_products

def main():
    driver = init_browser()
    
    try:
        # Test với vài categories
        test_categories = [
            ("Thuốc dành cho phụ nữ", "https://www.pharmacity.vn/thuoc-danh-cho-phu-nu"),
            ("Thuốc tiêu hóa", "https://www.pharmacity.vn/thuoc-tieu-hoa"),
            ("Thuốc cảm lạnh", "https://www.pharmacity.vn/thuoc-cam-lanh")
        ]
        
        total_products = 0
        
        for category_name, category_url in test_categories:
            print(f"\n{'='*80}")
            print(f"🏷️ TEST: {category_name}")
            print(f"📄 URL: {category_url}")
            print(f"{'='*80}")
            
            driver.get(category_url)
            time.sleep(5)
            
            # Đếm product-card ban đầu
            initial_cards = driver.find_elements(By.CLASS_NAME, "product-card")
            initial_count = len(initial_cards)
            print(f"📊 Product-card ban đầu: {initial_count}")
            
            # Click "Xem thêm" cho đến khi hết
            final_card_count = click_load_more_until_gone(driver)
            
            # Đếm link sản phẩm với logic mới (không lọc)
            link_count, product_links = count_all_html_links(driver)
            
            print(f"\n📊 KẾT QUẢ:")
            print(f"  📈 Product-card: {initial_count} → {final_card_count}")
            print(f"  🔗 Link .html (logic mới): {link_count}")
            print(f"  🎯 Kết luận: '{category_name}' có {link_count} sản phẩm")
            
            total_products += link_count
        
        print(f"\n{'='*80}")
        print(f"📊 TỔNG KẾT VỚI LOGIC MỚI")
        print(f"{'='*80}")
        print(f"🏷️ Số categories đã test: {len(test_categories)}")
        print(f"📦 Tổng sản phẩm: {total_products}")
        print(f"📈 Trung bình mỗi category: {total_products/len(test_categories):.1f} sản phẩm")
        
        print(f"\n🎯 KẾT LUẬN:")
        print(f"✅ Logic mới đã lấy được TẤT CẢ sản phẩm trên trang!")
        print(f"✅ Không còn bỏ sót sản phẩm do lọc sai từ khóa!")
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
    
    finally:
        driver.quit()
        print("🎉 Hoàn thành test logic mới!")

if __name__ == "__main__":
    main()
