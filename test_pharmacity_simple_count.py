from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def init_browser():
    """Khởi tạo browser full screen"""
    print("🚀 Khởi tạo Chrome browser FULL SCREEN...")
    service = Service(ChromeDriverManager().install())
    
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--force-device-scale-factor=1")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--log-level=3")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.maximize_window()
    
    window_size = driver.get_window_size()
    print(f"📐 Kích thước browser: {window_size['width']}x{window_size['height']}")
    print("✅ Chrome browser FULL SCREEN đã sẵn sàng!")
    return driver

def click_load_more_until_gone(driver):
    """Click nút Xem thêm liên tục cho đến khi nút biến mất"""
    print("\n🔄 Click 'Xem thêm' liên tục cho đến khi hết...")
    
    click_count = 0
    
    while True:
        try:
            # Đếm số product-card hiện tại
            product_cards = driver.find_elements(By.CLASS_NAME, "product-card")
            current_count = len(product_cards)
            print(f"  Lần {click_count + 1}: {current_count} product-card")
            
            # Scroll xuống cuối trang
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            
            # Tìm nút "Xem thêm" với selector chính xác
            try:
                # Thử tìm nút bằng XPath
                load_more_btn = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, "//button//span[text()='Xem thêm']"))
                )
                
                if load_more_btn and load_more_btn.is_displayed():
                    # Scroll đến nút
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", load_more_btn)
                    time.sleep(1)
                    
                    # Click nút
                    try:
                        load_more_btn.click()
                        print(f"    ✅ Đã click 'Xem thêm'")
                        click_count += 1
                        time.sleep(4)  # Chờ load sản phẩm mới
                    except:
                        # Thử click bằng JavaScript
                        driver.execute_script("arguments[0].click();", load_more_btn)
                        print(f"    ✅ Đã click 'Xem thêm' bằng JS")
                        click_count += 1
                        time.sleep(4)
                else:
                    print(f"    🛑 Nút 'Xem thêm' không hiển thị - Đã hết sản phẩm")
                    break
                    
            except TimeoutException:
                print(f"    🛑 Không tìm thấy nút 'Xem thêm' - Đã hết sản phẩm")
                break
            except Exception as e:
                print(f"    🛑 Lỗi khi tìm nút 'Xem thêm': {e}")
                break
                
        except Exception as e:
            print(f"    ❌ Lỗi: {e}")
            break
    
    # Đếm số product-card cuối cùng
    final_product_cards = driver.find_elements(By.CLASS_NAME, "product-card")
    final_count = len(final_product_cards)
    
    print(f"✅ Hoàn thành sau {click_count} lần click")
    print(f"📊 Tổng số product-card: {final_count}")
    
    return final_count

def test_category_simple(driver, category_url, category_name):
    """Test một category với cách đếm đơn giản"""
    print(f"\n{'='*80}")
    print(f"🏷️ TEST: {category_name}")
    print(f"📄 URL: {category_url}")
    print(f"{'='*80}")
    
    try:
        driver.get(category_url)
        time.sleep(5)
        
        # Đếm product-card ban đầu
        initial_cards = driver.find_elements(By.CLASS_NAME, "product-card")
        initial_count = len(initial_cards)
        print(f"📊 Product-card ban đầu: {initial_count}")
        
        # Click "Xem thêm" cho đến khi hết
        final_count = click_load_more_until_gone(driver)
        
        print(f"\n📊 KẾT QUẢ:")
        print(f"  📈 Từ {initial_count} → {final_count} product-card")
        print(f"  🎯 Tổng sản phẩm trong '{category_name}': {final_count}")
        
        return final_count
        
    except Exception as e:
        print(f"  ❌ Lỗi: {e}")
        return 0

def main():
    driver = init_browser()
    
    try:
        # Test các category chính của Pharmacity
        test_categories = [
            ("Thuốc tiêu hóa", "https://www.pharmacity.vn/thuoc-tieu-hoa"),
            ("Thuốc cảm lạnh", "https://www.pharmacity.vn/thuoc-cam-lanh"),
            ("Giảm đau hạ sốt", "https://www.pharmacity.vn/giam-dau-ha-sot"),
            ("Thuốc không kê đơn", "https://www.pharmacity.vn/thuoc-khong-ke-don"),
            ("Thuốc kháng dị ứng", "https://www.pharmacity.vn/thuoc-khang-di-ung"),
            ("Thuốc mắt/tai/mũi", "https://www.pharmacity.vn/thuoc-mattaimui"),
            ("Thuốc da liễu", "https://www.pharmacity.vn/thuoc-da-lieu"),
            ("Thuốc dành cho phụ nữ", "https://www.pharmacity.vn/thuoc-danh-cho-phu-nu"),
            ("Thuốc cơ xương khớp", "https://www.pharmacity.vn/thuoc-co-xuong-khop"),
            ("Vitamin và khoáng chất", "https://www.pharmacity.vn/vitamin-va-khoang-chat"),
            ("Thực phẩm chức năng", "https://www.pharmacity.vn/thuc-pham-chuc-nang"),
            ("Mẹ và bé", "https://www.pharmacity.vn/me-va-be"),
            ("Chăm sóc cá nhân", "https://www.pharmacity.vn/cham-soc-ca-nhan"),
            ("Chăm sóc sắc đẹp", "https://www.pharmacity.vn/cham-soc-sac-dep"),
            ("Thiết bị y tế", "https://www.pharmacity.vn/thiet-bi-y-te-2")
        ]
        
        category_results = []
        total_products = 0
        
        for category_name, category_url in test_categories:
            count = test_category_simple(driver, category_url, category_name)
            category_results.append((category_name, count))
            total_products += count
        
        # Tổng kết
        print(f"\n{'='*80}")
        print(f"📊 TỔNG KẾT PHARMACITY - ĐẾM PRODUCT-CARD")
        print(f"{'='*80}")
        
        print(f"\n📋 KẾT QUẢ THEO CATEGORY:")
        for name, count in category_results:
            print(f"  {name}: {count} sản phẩm")
        
        print(f"\n📊 THỐNG KÊ TỔNG:")
        print(f"🏷️ Số categories đã test: {len(test_categories)}")
        print(f"📦 Tổng sản phẩm (có thể trùng lặp): {total_products}")
        
        # Ước tính số sản phẩm unique
        avg_per_category = total_products / len(test_categories) if test_categories else 0
        print(f"📈 Trung bình mỗi category: {avg_per_category:.1f} sản phẩm")
        
        print(f"\n🎯 KẾT LUẬN:")
        if total_products > 1000:
            print(f"🎉 Pharmacity có {total_products} sản phẩm - RẤT NHIỀU!")
        elif total_products > 500:
            print(f"✅ Pharmacity có {total_products} sản phẩm - Khá nhiều!")
        else:
            print(f"⚠️ Pharmacity có {total_products} sản phẩm")
        
        # Top categories
        print(f"\n🏆 TOP CATEGORIES CÓ NHIỀU SẢN PHẨM NHẤT:")
        sorted_categories = sorted(category_results, key=lambda x: x[1], reverse=True)
        for i, (name, count) in enumerate(sorted_categories[:10]):
            print(f"  {i+1}. {name}: {count} sản phẩm")
        
        # Lưu kết quả
        with open("pharmacity_product_card_count.txt", "w", encoding="utf-8") as f:
            f.write("PHARMACITY PRODUCT COUNT BY CATEGORY\n")
            f.write("="*50 + "\n\n")
            f.write(f"Tổng sản phẩm: {total_products}\n")
            f.write(f"Số categories: {len(test_categories)}\n")
            f.write(f"Trung bình/category: {avg_per_category:.1f}\n\n")
            
            f.write("Chi tiết theo category:\n")
            for name, count in sorted_categories:
                f.write(f"{name}: {count} sản phẩm\n")
        
        print(f"\n💾 Đã lưu kết quả vào: pharmacity_product_card_count.txt")
        
    except Exception as e:
        print(f"❌ Lỗi tổng quát: {e}")
    
    finally:
        driver.quit()
        print("🎉 Hoàn thành đếm product-card Pharmacity!")

if __name__ == "__main__":
    main()
