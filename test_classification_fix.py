import requests
from bs4 import BeautifulSoup

def test_product_classification(url):
    """Test phân loại sản phẩm với logic mới"""
    print(f"🧪 Test phân loại sản phẩm: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        product_soup = BeautifulSoup(response.content, 'html.parser')
        
        # Logic mới từ crawl_pharmacity.py
        product_classifications = []
        classification_keywords = ['Kit', 'Hộp', 'Chai', 'Lọ', 'Vỉ', 'Gói', 'Bộ', 'Tuýp', 'Viên', 'Túi']
        
        # Tìm thẻ label có text "Phân loại sản phẩm"
        classification_labels = product_soup.find_all('label', string=lambda text: text and 'Phân loại sản phẩm' in text)
        
        for label in classification_labels:
            # Tìm div ngay sau label này
            next_div = label.find_next_sibling('div')
            if next_div:
                # Tìm tất cả button trong div này
                buttons = next_div.find_all('button')
                
                for button in buttons:
                    # Tìm span trong button
                    spans = button.find_all('span')
                    
                    for span in spans:
                        span_text = span.get_text(strip=True)
                        
                        # Lưu text span nếu không trống và không phải là số
                        if span_text and not span_text.isdigit() and span_text not in product_classifications:
                            # Kiểm tra nếu là từ khóa phân loại hợp lệ
                            if any(keyword.lower() in span_text.lower() for keyword in classification_keywords):
                                product_classifications.append(span_text)
        
        # Nếu không tìm thấy bằng cách trên, thử tìm theo cấu trúc khác
        if not product_classifications:
            # Tìm tất cả div có chứa text "Phân loại sản phẩm"
            all_divs = product_soup.find_all('div')
            for div in all_divs:
                if 'Phân loại sản phẩm' in div.get_text():
                    # Tìm button trong div này
                    buttons = div.find_all('button')
                    
                    for button in buttons:
                        spans = button.find_all('span')
                        for span in spans:
                            span_text = span.get_text(strip=True)
                            if span_text and not span_text.isdigit() and span_text not in product_classifications:
                                # Kiểm tra nếu là từ khóa phân loại hợp lệ
                                if any(keyword.lower() in span_text.lower() for keyword in classification_keywords):
                                    product_classifications.append(span_text)
        
        # Nếu vẫn không tìm thấy, thử tìm trong text tổng thể
        if not product_classifications:
            page_text = product_soup.get_text()
            for keyword in classification_keywords:
                if keyword in page_text:
                    product_classifications.append(keyword)
                    break
        
        # Gộp tất cả phân loại thành chuỗi
        if product_classifications:
            product_classification = ", ".join(product_classifications)
        else:
            product_classification = "Không có"
        
        print(f"✅ Kết quả: {product_classification}")
        return product_classification
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return "Không có"

if __name__ == "__main__":
    # Test với các sản phẩm khác nhau
    test_urls = [
        "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html",  # Chỉ có Hộp
        "https://www.pharmacity.vn/genesign-test-respiratory-combo-antigen-rapid-20-test-hop.html",  # Có Kit và Hộp
        "https://www.pharmacity.vn/vien-nang-bidiphar-khong-co.html",  # Sản phẩm có Vỉ
    ]
    
    for i, test_url in enumerate(test_urls):
        print(f"\n{'='*80}")
        print(f"TEST {i+1}")
        classification = test_product_classification(test_url)
        print(f"🎯 Phân loại cuối cùng: {classification}")
