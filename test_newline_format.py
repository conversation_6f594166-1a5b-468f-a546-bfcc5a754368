import csv

def test_newline_format():
    """Test format ảnh với dấu xuống dòng"""
    
    # Tạo dữ liệu test
    test_data = [
        ["URL", "Tên sản phẩm", "Ảnh sản phẩm"],
        [
            "https://example.com/product1", 
            "Sản phẩm test 1",
            "https://image1.jpg\nhttps://image2.jpg\nhttps://image3.jpg"
        ],
        [
            "https://example.com/product2", 
            "Sản phẩm test 2", 
            "https://imageA.jpg\nhttps://imageB.jpg\nhttps://imageC.jpg\nhttps://imageD.jpg"
        ]
    ]
    
    # Ghi vào CSV
    with open('test_newline_images.csv', 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerows(test_data)
    
    print("✅ Đã tạo file test_newline_images.csv")
    print("📋 Nội dung file:")
    
    # Đọc và hiển thị nội dung
    with open('test_newline_images.csv', 'r', encoding='utf-8') as csvfile:
        content = csvfile.read()
        print(content)
    
    print("\n📊 Khi import vào Google Sheets:")
    print("- Mỗi URL ảnh sẽ hiển thị trên một dòng riêng trong cùng một cell")
    print("- Cell sẽ tự động mở rộng chiều cao để hiển thị tất cả ảnh")
    print("- Bạn có thể wrap text để xem đầy đủ các URL")

if __name__ == "__main__":
    test_newline_format()
