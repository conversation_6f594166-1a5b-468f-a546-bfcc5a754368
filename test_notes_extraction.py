import requests
from bs4 import BeautifulSoup
import re

def test_notes_extraction(url):
    """Test việc lấy lưu ý từ một sản phẩm"""
    print(f"🔍 Test lấy lưu ý từ: {url}")

    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()

        soup = BeautifulSoup(response.content, 'html.parser')

        print(f"📄 Tìm kiếm thẻ p có text 'Lưu ý'...")

        # Tìm tất cả thẻ p chứa text "Lưu ý"
        luu_y_elements = soup.find_all('p', string=lambda text: text and 'Lưu ý' in text)
        print(f"🔍 Tìm thấy {len(luu_y_elements)} thẻ p có 'Lưu ý'")

        for i, luu_y_p in enumerate(luu_y_elements):
            print(f"\n📋 Thẻ p thứ {i+1}: {repr(luu_y_p.get_text())}")

            # Tìm div ngay sau thẻ p này
            next_div = luu_y_p.find_next_sibling('div')
            if next_div:
                notes_text = next_div.get_text(strip=True)
                print(f"✅ Div tiếp theo: {repr(notes_text[:100])}...")
                if len(notes_text) > 10:
                    print(f"✅ Nội dung lưu ý hợp lệ!")
                    return notes_text
            else:
                print(f"❌ Không tìm thấy div tiếp theo")

        # Nếu không tìm thấy, thử tìm bằng cách khác
        print(f"\n🔍 Tìm theo cấu trúc grid...")
        grid_elements = soup.find_all('div', class_=lambda x: x and 'grid' in x)
        print(f"🔍 Tìm thấy {len(grid_elements)} grid elements")

        for i, grid in enumerate(grid_elements):
            if 'Lưu ý' in grid.get_text():
                print(f"\n📋 Grid thứ {i+1} có 'Lưu ý'")
                print(f"Grid HTML: {str(grid)[:200]}...")

                # Tìm div chứa nội dung lưu ý trong grid này
                content_divs = grid.find_all('div')
                for j, div in enumerate(content_divs):
                    div_text = div.get_text(strip=True)
                    if div_text and len(div_text) > 20 and 'Lưu ý' not in div_text:
                        print(f"✅ Div con thứ {j+1}: {repr(div_text[:100])}...")
                        return div_text

        # Thử tìm bằng cách khác - tìm tất cả text có "Lưu ý"
        print(f"\n🔍 Tìm tất cả elements có text 'Lưu ý'...")
        all_elements = soup.find_all(string=lambda text: text and 'Lưu ý' in text)
        print(f"🔍 Tìm thấy {len(all_elements)} elements có 'Lưu ý'")

        for i, element in enumerate(all_elements[:5]):  # Chỉ xem 5 đầu tiên
            print(f"  {i+1}. {repr(str(element)[:50])}...")
            parent = element.parent
            if parent:
                print(f"     Parent: {parent.name} - {parent.get('class', [])}")
                next_sibling = parent.find_next_sibling()
                if next_sibling:
                    sibling_text = next_sibling.get_text(strip=True)
                    if len(sibling_text) > 20:
                        print(f"     Next sibling text: {repr(sibling_text[:100])}...")

        return "Không tìm thấy"

    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return None

if __name__ == "__main__":
    # Test với nhiều sản phẩm
    test_urls = [
        "https://www.pharmacity.vn/genesign-test-respiratory-combo-antigen-rapid-20-test-hop.html",
        "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html"
    ]

    for i, test_url in enumerate(test_urls):
        print(f"\n{'='*60}")
        print(f"TEST {i+1}")
        notes = test_notes_extraction(test_url)

        if notes and notes != "Không tìm thấy":
            print(f"\n✅ Thành công lấy lưu ý:")
            print(f"📝 {notes}")
        else:
            print(f"\n❌ Không lấy được lưu ý!")
