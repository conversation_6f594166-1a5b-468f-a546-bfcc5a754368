from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def init_browser():
    """Khởi tạo browser full screen"""
    print("🚀 Khởi tạo Chrome browser FULL SCREEN...")
    service = Service(ChromeDriverManager().install())
    
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--force-device-scale-factor=1")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--log-level=3")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.maximize_window()
    
    window_size = driver.get_window_size()
    print(f"📐 Kích thước browser: {window_size['width']}x{window_size['height']}")
    print("✅ Chrome browser FULL SCREEN đã sẵn sàng!")
    return driver

def click_load_more_until_gone(driver):
    """Click nút Xem thêm liên tục cho đến khi nút biến mất"""
    print("\n🔄 Click 'Xem thêm' liên tục cho đến khi hết...")
    
    click_count = 0
    
    while True:
        try:
            # Đếm số product-card hiện tại
            product_cards = driver.find_elements(By.CLASS_NAME, "product-card")
            current_count = len(product_cards)
            print(f"  Lần {click_count + 1}: {current_count} product-card")
            
            # Scroll xuống cuối trang
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            
            # Tìm nút "Xem thêm" với selector chính xác
            try:
                # Thử tìm nút bằng XPath
                load_more_btn = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, "//button//span[text()='Xem thêm']"))
                )
                
                if load_more_btn and load_more_btn.is_displayed():
                    # Scroll đến nút
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", load_more_btn)
                    time.sleep(1)
                    
                    # Click nút
                    try:
                        load_more_btn.click()
                        print(f"    ✅ Đã click 'Xem thêm'")
                        click_count += 1
                        time.sleep(4)  # Chờ load sản phẩm mới
                    except:
                        # Thử click bằng JavaScript
                        driver.execute_script("arguments[0].click();", load_more_btn)
                        print(f"    ✅ Đã click 'Xem thêm' bằng JS")
                        click_count += 1
                        time.sleep(4)
                else:
                    print(f"    🛑 Nút 'Xem thêm' không hiển thị - Đã hết sản phẩm")
                    break
                    
            except TimeoutException:
                print(f"    🛑 Không tìm thấy nút 'Xem thêm' - Đã hết sản phẩm")
                break
            except Exception as e:
                print(f"    🛑 Lỗi khi tìm nút 'Xem thêm': {e}")
                break
                
        except Exception as e:
            print(f"    ❌ Lỗi: {e}")
            break
    
    # Đếm số product-card cuối cùng
    final_product_cards = driver.find_elements(By.CLASS_NAME, "product-card")
    final_count = len(final_product_cards)
    
    print(f"✅ Hoàn thành sau {click_count} lần click")
    print(f"📊 Tổng số product-card: {final_count}")
    
    return final_count

def analyze_product_cards_vs_links(driver):
    """Phân tích chi tiết product-card vs links"""
    print(f"\n{'='*80}")
    print(f"🔍 PHÂN TÍCH CHI TIẾT PRODUCT-CARD VS LINKS")
    print(f"{'='*80}")
    
    # Đếm product-card
    product_cards = driver.find_elements(By.CLASS_NAME, "product-card")
    card_count = len(product_cards)
    print(f"📊 Số product-card: {card_count}")
    
    # Phân tích từng product-card
    card_with_links = 0
    card_without_links = 0
    all_links_from_cards = []
    
    for i, card in enumerate(product_cards[:10]):  # Chỉ phân tích 10 card đầu để debug
        try:
            # Tìm link trong card này
            links_in_card = card.find_elements(By.TAG_NAME, "a")
            
            if links_in_card:
                card_with_links += 1
                print(f"  Card {i+1}: Có {len(links_in_card)} link(s)")
                
                for j, link in enumerate(links_in_card):
                    href = link.get_attribute("href")
                    if href and ".html" in href:
                        if "?" in href:
                            clean_href = href.split("?")[0]
                        else:
                            clean_href = href
                        
                        if clean_href not in all_links_from_cards:
                            all_links_from_cards.append(clean_href)
                            print(f"    Link {j+1}: {clean_href}")
            else:
                card_without_links += 1
                print(f"  Card {i+1}: Không có link")
                
        except Exception as e:
            print(f"  Card {i+1}: Lỗi - {e}")
    
    # Đếm tất cả link .html trên trang
    soup = BeautifulSoup(driver.page_source, "html.parser")
    all_links = soup.find_all("a", href=True)
    
    unique_html_links = []
    for link in all_links:
        href = link.get("href")
        if href and ".html" in href:
            if href.startswith("/"):
                full_url = "https://www.pharmacity.vn" + href
            elif href.startswith("https://www.pharmacity.vn"):
                full_url = href
            else:
                continue
            
            if "?" in full_url:
                clean_url = full_url.split("?")[0]
            else:
                clean_url = full_url
            
            if clean_url not in unique_html_links:
                unique_html_links.append(clean_url)
    
    print(f"\n📊 TỔNG KẾT:")
    print(f"  📦 Tổng product-card: {card_count}")
    print(f"  ✅ Card có link: {card_with_links}")
    print(f"  ❌ Card không có link: {card_without_links}")
    print(f"  🔗 Link unique từ cards (10 đầu): {len(all_links_from_cards)}")
    print(f"  🌐 Tổng link .html unique trên trang: {len(unique_html_links)}")
    
    print(f"\n💡 GIẢI THÍCH SỰ KHÁC BIỆT:")
    if len(unique_html_links) < card_count:
        print(f"  • Có {card_count - len(unique_html_links)} product-card không có link .html")
        print(f"  • Hoặc có link trùng lặp giữa các card")
    elif len(unique_html_links) > card_count:
        print(f"  • Có thêm {len(unique_html_links) - card_count} link .html từ các phần khác trên trang")
        print(f"  • (Không phải từ product-card)")
    else:
        print(f"  • Số lượng khớp hoàn toàn!")
    
    return card_count, len(unique_html_links), unique_html_links

def main():
    driver = init_browser()
    
    try:
        # Test với category thuốc cảm lạnh
        category_url = "https://www.pharmacity.vn/thuoc-cam-lanh"
        category_name = "Thuốc cảm lạnh"
        
        print(f"\n{'='*80}")
        print(f"🏷️ DEBUG: {category_name}")
        print(f"📄 URL: {category_url}")
        print(f"{'='*80}")
        
        driver.get(category_url)
        time.sleep(5)
        
        # Click "Xem thêm" cho đến khi hết
        final_card_count = click_load_more_until_gone(driver)
        
        # Phân tích chi tiết
        card_count, link_count, all_links = analyze_product_cards_vs_links(driver)
        
        print(f"\n{'='*80}")
        print(f"📊 KẾT QUẢ CUỐI CÙNG")
        print(f"{'='*80}")
        print(f"📦 Product-card: {card_count}")
        print(f"🔗 Link .html unique: {link_count}")
        print(f"📈 Chênh lệch: {abs(card_count - link_count)}")
        
        # Lưu tất cả link để kiểm tra
        with open("debug_cam_lanh_links.txt", "w", encoding="utf-8") as f:
            f.write(f"THUỐC CẢM LẠNH - DEBUG\n")
            f.write(f"="*50 + "\n\n")
            f.write(f"Product-card: {card_count}\n")
            f.write(f"Link .html unique: {link_count}\n")
            f.write(f"Chênh lệch: {abs(card_count - link_count)}\n\n")
            
            f.write(f"TẤT CẢ LINK .HTML ({len(all_links)}):\n")
            f.write(f"="*50 + "\n")
            for i, url in enumerate(all_links):
                f.write(f"{i+1}. {url}\n")
        
        print(f"\n💾 Đã lưu debug info vào: debug_cam_lanh_links.txt")
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
    
    finally:
        driver.quit()
        print("🎉 Hoàn thành debug!")

if __name__ == "__main__":
    main()
