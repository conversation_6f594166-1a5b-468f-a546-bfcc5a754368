from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import time
import gspread
from google.oauth2.service_account import Credentials
import re

def init_browser():
    """Khởi tạo Chrome browser với các tùy chọn tối ưu"""
    print("Đang khởi tạo Chrome browser...")
    
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    
    driver = webdriver.Chrome(options=chrome_options)
    print("✅ Chrome browser đã sẵn sàng!")
    return driver

def init_google_sheets():
    """Khởi tạo kết nối Google Sheets"""
    scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
    creds = Credentials.from_service_account_file("service_account.json", scopes=scope)
    client = gspread.authorize(creds)
    
    # Mở sheet theo tên
    sheet = client.open("Medical Products Data").sheet1
    return sheet

def crawl_product_detail(driver, product_url):
    """Crawl chi tiết một sản phẩm"""
    try:
        driver.get(product_url)
        time.sleep(3)
        
        # Lấy HTML và parse
        html = driver.page_source
        product_soup = BeautifulSoup(html, 'html.parser')
        
        # Khởi tạo dữ liệu mặc định
        product_name = "Không có"
        product_code = "Không có"
        brand = "Không có"
        price = "Không có"
        category = "Không có"
        manufacturer = "Không có"
        registration_number = "Không có"
        prescription_required = "Không"
        active_ingredient = "Không có"
        indication = "Không có"
        target_user = "Không có"
        dosage_form = "Không có"
        specification = "Không có"
        usage = "Không có"
        
        # Lấy tên sản phẩm
        title_selectors = ['h1', '.product-title', '.product-name']
        for selector in title_selectors:
            title_element = product_soup.select_one(selector)
            if title_element:
                product_name = title_element.text.strip()
                break
        
        # Lấy mã sản phẩm từ text
        page_text = product_soup.get_text()
        code_match = re.search(r'P\d{5}', page_text)
        if code_match:
            product_code = code_match.group()
        
        # Lấy thương hiệu
        brand_link = product_soup.find('a', href=lambda x: x and '/thuong-hieu/' in x)
        if brand_link:
            brand = brand_link.text.strip().replace("Thương hiệu: ", "")
        
        # Lấy giá
        price_match = re.search(r'(\d+\.?\d*)\s*₫', page_text)
        if price_match:
            price = price_match.group(0)
        
        # Lấy số đăng ký
        reg_patterns = [
            r'Số đăng ký:\s*(\d+)',
            r'(\d{12})',
        ]
        for pattern in reg_patterns:
            reg_match = re.search(pattern, page_text)
            if reg_match:
                registration_number = reg_match.group(1) if reg_match.group(1).isdigit() else reg_match.group(0)
                break
        
        # Parse thông tin từ text
        # Tìm danh mục
        category_patterns = [
            r'Danh mục([^A-Z]*?)Nhà sản xuất',
            r'Danh mục([^A-Z]*?)(?=Nhà|Hoạt|Chỉ|Dạng|Quy)',
            r'Danh mục(.*?)(?=Nhà sản xuất|Hoạt chất)'
        ]
        for pattern in category_patterns:
            match = re.search(pattern, page_text)
            if match:
                cat_text = match.group(1).strip()
                if "..." in cat_text:
                    category = cat_text.split("...")[-1].strip()
                else:
                    category = cat_text
                break
        
        # Tìm nhà sản xuất
        manufacturer_patterns = [
            r'Nhà sản xuất([^A-Z]*?)Hoạt chất',
            r'Nhà sản xuất([^A-Z]*?)(?=Hoạt|Chỉ|Dạng|Quy)',
            r'Nhà sản xuất(.*?)(?=Hoạt chất|Chỉ định)'
        ]
        for pattern in manufacturer_patterns:
            match = re.search(pattern, page_text)
            if match:
                manufacturer = match.group(1).strip()
                break
        
        # Tìm hoạt chất
        active_patterns = [
            r'Hoạt chất([^A-Z]*?)Chỉ định',
            r'Hoạt chất([^A-Z]*?)(?=Chỉ|Dạng|Quy)',
            r'Hoạt chất(.*?)(?=Chỉ định|Dạng bào chế)'
        ]
        for pattern in active_patterns:
            match = re.search(pattern, page_text)
            if match:
                active_ingredient = match.group(1).strip()
                break
        
        # Tìm chỉ định
        indication_patterns = [
            r'Chỉ định([^A-Z]*?)Dạng bào chế',
            r'Chỉ định([^A-Z]*?)(?=Dạng|Quy)',
            r'Chỉ định(.*?)(?=Dạng bào chế|Quy cách)'
        ]
        for pattern in indication_patterns:
            match = re.search(pattern, page_text)
            if match:
                indication = match.group(1).strip()
                break
        
        # Tìm dạng bào chế
        dosage_patterns = [
            r'Dạng bào chế([^A-Z]*?)Quy cách',
            r'Dạng bào chế([^A-Z]*?)(?=Quy)',
            r'Dạng bào chế(.*?)(?=Quy cách|Lưu ý)'
        ]
        for pattern in dosage_patterns:
            match = re.search(pattern, page_text)
            if match:
                dosage_form = match.group(1).strip()
                break
        
        # Tìm quy cách
        spec_patterns = [
            r'Quy cách([^A-Z]*?)Lưu ý',
            r'Quy cách([^A-Z]*?)(?=Lưu|Đủ)',
            r'Quy cách(.*?)(?=Lưu ý|Đủ thuốc)'
        ]
        for pattern in spec_patterns:
            match = re.search(pattern, page_text)
            if match:
                specification = match.group(1).strip()
                break
        
        # Đối tượng sử dụng
        if "Trẻ nhỏ dưới" in page_text:
            target_user = "Người lớn và trẻ em trên 5 tuổi"
        elif "Phụ nữ có thai" in page_text and "chống chỉ định" in page_text.lower():
            target_user = "Không dành cho phụ nữ có thai"
        else:
            target_user = "Người lớn"
        
        # Tìm cách dùng
        usage_patterns = [
            r'Bôi.*?ngày\s*\d+\s*-\s*\d+\s*lần',
            r'\d+\s*viên.*?ngày',
            r'Liều dùng.*?ngày.*?lần',
            r'Cách dùng.*?ngày.*?lần'
        ]
        
        for pattern in usage_patterns:
            usage_match = re.search(pattern, page_text, re.IGNORECASE)
            if usage_match:
                usage = usage_match.group(0)
                break
        
        return [
            product_url, product_name, product_code, brand, price, category,
            manufacturer, registration_number, prescription_required,
            active_ingredient, indication, target_user, dosage_form,
            specification, usage
        ]
        
    except Exception as e:
        print(f"Lỗi khi crawl {product_url}: {e}")
        return None

def main():
    # Khởi tạo browser
    driver = init_browser()
    
    # Khởi tạo Google Sheets
    sheet = init_google_sheets()
    
    # Thêm header
    headers = ["URL", "Tên sản phẩm", "Mã sản phẩm", "Thương hiệu", "Giá", "Danh mục", "Nhà sản xuất", "Số đăng ký", "Thuốc kê đơn", "Hoạt chất", "Chỉ định", "Đối tượng sử dụng", "Dạng bào chế", "Quy cách", "Cách dùng"]
    sheet.update("A1:O1", [headers])
    
    # Test với sản phẩm Siang Pure Oil
    test_url = "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html"
    print(f"🧪 Test crawl: {test_url}")
    
    try:
        product_data = crawl_product_detail(driver, test_url)
        if product_data:
            print("✅ Crawl thành công!")
            print("📋 Dữ liệu:")
            for i, header in enumerate(headers):
                print(f"  {header}: {product_data[i]}")
            
            # Ghi vào Google Sheets
            sheet.update("A2:O2", [product_data])
            print("✅ Đã ghi vào Google Sheets!")
        else:
            print("❌ Không crawl được dữ liệu")
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
    
    # Đóng browser
    driver.quit()
    print("🎉 Hoàn thành!")

if __name__ == "__main__":
    main()
