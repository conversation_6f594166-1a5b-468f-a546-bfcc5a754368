from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup
import time
import gspread
from oauth2client.service_account import ServiceAccountCredentials

# ===== CẤU HÌNH =====
BASE_URL = "https://www.pharmacity.vn/thiet-bi-y-te-2"
MAX_PRODUCTS = 1200  # Số sản phẩm tối đa muốn lấy (tăng để đủ skip + crawl mới)

# HƯỚNG DẪN SỬ DỤNG START_ROW:
# - START_ROW = 2: Bắt đầu từ đầu (tạo header mới)
# - START_ROW = 1001: Tiếp tục từ dòng 1001 (đã có 1000 dòng data)
# - START_ROW = 501: Tiếp tục từ dòng 501 (đã có 500 dòng data)
START_ROW = 1127  # Dòng bắt đầu ghi data

# Hàm kết nối Google Sheets
def connect_to_google_sheets():
    scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
    # Sử dụng file JSON mới
    creds = ServiceAccountCredentials.from_json_keyfile_name("medical-crawl-2024-013b6faaa588.json", scope)
    client = gspread.authorize(creds)
    # Kết nối đến sheet MedicalDevice thay vì Medicine
    sheet = client.open_by_key("1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8").worksheet("MedicalDevice")
    return sheet

def click_load_more_buttons(driver):
    """
    Bấm nút "Xem thêm" cho đến khi không còn nút nào để bấm
    """
    print("🔄 Bắt đầu bấm nút 'Xem thêm'...")
    click_count = 0
    
    while True:
        try:
            # Tìm nút "Xem thêm" với selector cụ thể
            load_more_button = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(@class, 'border-primary-500') and contains(@class, 'text-primary-500')]//span[text()='Xem thêm']"))
            )
            
            # Scroll đến nút để đảm bảo nó hiển thị
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", load_more_button)
            time.sleep(1)
            
            # Bấm nút
            load_more_button.click()
            click_count += 1
            print(f"✅ Đã bấm nút 'Xem thêm' lần {click_count}")
            
            # Chờ trang load thêm sản phẩm
            time.sleep(3)
            
        except TimeoutException:
            print(f"🏁 Không còn nút 'Xem thêm' nào. Đã bấm tổng cộng {click_count} lần")
            break
        except Exception as e:
            print(f"⚠️ Lỗi khi bấm nút 'Xem thêm': {e}")
            break
    
    return click_count

def get_product_links(driver):
    """
    Lấy tất cả link sản phẩm từ trang hiện tại
    """
    print("🔍 Đang lấy danh sách link sản phẩm...")
    
    # Lấy HTML của trang
    soup = BeautifulSoup(driver.page_source, "html.parser")
    
    # Tìm tất cả thẻ a có href chứa "/thiet-bi-y-te" hoặc có class product-related
    product_links = []
    
    # Tìm các link sản phẩm
    links = soup.find_all('a', href=True)
    
    for link in links:
        href = link.get('href')
        if href:
            # Chuyển đổi URL tương đối thành URL tuyệt đối
            if href.startswith('/'):
                href = 'https://www.pharmacity.vn' + href
            
            # Lọc các link sản phẩm (có thể chứa tên sản phẩm hoặc mã sản phẩm)
            if ('pharmacity.vn' in href and 
                href != BASE_URL and 
                not any(skip in href.lower() for skip in ['category', 'brand', 'search', 'cart', 'account', 'login', 'register', 'thiet-bi-y-te-2'])):
                
                # Kiểm tra xem có phải link sản phẩm không (thường có dạng tên-san-pham.html)
                if href.endswith('.html') or '/p/' in href:
                    if href not in product_links:
                        product_links.append(href)
    
    print(f"✅ Tìm thấy {len(product_links)} link sản phẩm")
    return product_links

def get_product_images(driver, url):
    """
    Lấy link ảnh sản phẩm từ URL - Logic giống crawl_pharmacity_images.py
    """
    try:
        print(f"🔍 Đang truy cập để lấy ảnh: {url}")

        # Thử truy cập URL
        try:
            driver.get(url)
            time.sleep(3)  # Chờ trang load
        except Exception as e:
            print(f"❌ Không thể truy cập URL: {e}")
            return []

        # Kiểm tra nếu trang load thành công
        if "404" in driver.title.lower() or "not found" in driver.title.lower():
            print("❌ Trang không tồn tại (404)")
            return []

        soup = BeautifulSoup(driver.page_source, "html.parser")

        # Tìm phần chứa ảnh sản phẩm chính
        image_urls = []

        # Method 1: Tìm swiper chứa ảnh sản phẩm chính (product-media-slide)
        product_swiper = soup.find('div', class_=lambda x: x and 'product-media-slide' in str(x))

        if product_swiper:
            print("✅ Tìm thấy product-media-slide container")
            images = product_swiper.find_all('img')

            for img in images:
                src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
                if src:
                    # Chuyển đổi URL tương đối thành URL tuyệt đối
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = 'https://www.pharmacity.vn' + src

                    # Cải thiện pattern nhận diện ảnh sản phẩm - bao gồm timestamp
                    import re
                    # Pattern: P + 5 chữ số + _ + số, có thể có timestamp ở đầu
                    # Ví dụ: P00126_1.png, 20241107092031-0-P00198_1.png
                    has_product_code = bool(re.search(r'P\d{5}_\d+\.(png|jpg|jpeg|webp)', src, re.IGNORECASE))

                    if has_product_code and 'ecommerce' in src.lower():
                        if src not in image_urls:
                            image_urls.append(src)
                            print(f"  📸 Ảnh sản phẩm: {src}")

        # Method 2: Nếu không tìm thấy, tìm theo pattern mã sản phẩm
        if not image_urls:
            print("🔍 Tìm ảnh theo pattern mã sản phẩm...")
            all_images = soup.find_all('img')

            for img in all_images:
                src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
                if src:
                    # Chuyển đổi URL tương đối thành URL tuyệt đối
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = 'https://www.pharmacity.vn' + src

                    # Cải thiện pattern nhận diện ảnh sản phẩm - bao gồm timestamp
                    import re
                    # Pattern: P + 5 chữ số + _ + số, có thể có timestamp ở đầu
                    # Ví dụ: P00126_1.png, 20241107092031-0-P00198_1.png
                    has_product_code = bool(re.search(r'P\d{5}_\d+\.(png|jpg|jpeg|webp)', src, re.IGNORECASE))

                    if has_product_code and 'ecommerce' in src.lower():
                        # Bỏ qua ảnh không liên quan
                        skip_keywords = ['banner', 'badge', 'icon', 'logo', 'frame', 'button', 'static-website']
                        if not any(keyword in src.lower() for keyword in skip_keywords):
                            if src not in image_urls:
                                image_urls.append(src)
                                print(f"  📸 Ảnh sản phẩm: {src}")

        # Nếu không tìm thấy ảnh theo pattern, tìm tất cả ảnh trong swiper-slide
        if not image_urls:
            print("🔍 Không tìm thấy ảnh theo pattern, tìm tất cả ảnh trong swiper-slide...")

            # Tìm tất cả div có class chứa "swiper-slide"
            swiper_slides = soup.find_all('div', class_=lambda x: x and 'swiper-slide' in str(x))

            for slide in swiper_slides:
                # Tìm tất cả thẻ img trong slide này
                images_in_slide = slide.find_all('img')

                for img in images_in_slide:
                    src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
                    if src:
                        # Chuyển đổi URL tương đối thành URL tuyệt đối
                        if src.startswith('//'):
                            src = 'https:' + src
                        elif src.startswith('/'):
                            src = 'https://www.pharmacity.vn' + src

                        # Bỏ qua ảnh không liên quan
                        skip_keywords = ['banner', 'badge', 'icon', 'logo', 'frame', 'button', 'static-website']
                        if not any(keyword in src.lower() for keyword in skip_keywords):
                            if src not in image_urls:
                                image_urls.append(src)
                                print(f"  📸 Ảnh từ swiper-slide: {src}")

        # Lấy tất cả ảnh sản phẩm, không quan tâm đến kích thước
        final_images = image_urls

        # Giới hạn tối đa 5 ảnh
        final_images = final_images[:5]

        print(f"✅ Tìm thấy {len(final_images)} ảnh sản phẩm")
        return final_images

    except Exception as e:
        print(f"❌ Lỗi khi lấy ảnh từ {url}: {e}")
        return []

def get_column_letter(index):
    """Chuyển đổi index cột thành tên cột (A, B, ..., Z, AA, AB, ...)"""
    if index < 26:
        return chr(ord('A') + index)
    else:
        # AA, AB, AC, ... (index 26, 27, 28, ...)
        first_letter = chr(ord('A') + (index // 26) - 1)
        second_letter = chr(ord('A') + (index % 26))
        return first_letter + second_letter

def ensure_sheet_columns(sheet, required_columns):
    """Đảm bảo sheet có đủ số cột cần thiết"""
    try:
        current_cols = sheet.col_count
        if current_cols < required_columns:
            sheet.add_cols(required_columns - current_cols)
            print(f"✅ Đã mở rộng sheet từ {current_cols} lên {required_columns} cột")
    except Exception as e:
        print(f"⚠️ Không thể mở rộng sheet: {e}")

def get_product_info(driver, url, sheet, row, dynamic_columns, fixed_header):
    """
    Lấy thông tin sản phẩm từ URL - Logic y hệt crawl_pharmacity.py
    """
    try:
        print(f"🔍 Đang crawl sản phẩm {row-1}: {url}")
        driver.get(url)
        time.sleep(1)
        product_soup = BeautifulSoup(driver.page_source, "html.parser")

        # Khởi tạo dữ liệu mặc định cho 18 cột
        product_name = "Không có"
        product_code = "Không có"
        brand = "Không có"
        price = "Không có"
        category = "Không có"
        manufacturer = "Không có"
        registration_number = "Không có"
        product_classification = "Không có"
        active_ingredient = "Không có"
        indication = "Không có"
        target_user = "Không có"
        dosage_form = "Không có"
        specification = "Không có"
        usage = "Không có"
        product_images = "Không có"

        # Khởi tạo cột từ pmc-content-html
        tong_hop = "Không có"  # Chỉ dùng cho TH1
        pmc_columns = {}  # Dictionary để lưu các cột động từ TH2

        # Lấy tên sản phẩm
        name_selectors = [
            'h1[data-testid="product-name"]',
            'h1.product-name',
            'h1',
            '.product-title h1',
            '[data-testid="product-title"]'
        ]
        for selector in name_selectors:
            name_element = product_soup.select_one(selector)
            if name_element:
                product_name = name_element.text.strip()
                break

        # Lấy mã sản phẩm từ cấu trúc HTML cụ thể
        import re

        # Phương pháp 1: Tìm span chứa mã sản phẩm bắt đầu bằng "P" (ưu tiên)
        all_spans = product_soup.find_all('span')
        for span in all_spans:
            span_text = span.get_text(strip=True)
            # Tìm mã sản phẩm bắt đầu bằng P và có 5-6 chữ số
            if re.match(r'^P\d{5,6}$', span_text):
                product_code = span_text
                break

        # Phương pháp 2: Nếu không tìm thấy, tìm trong text gần thương hiệu
        if product_code == "Không có":
            # Tìm thẻ a chứa thương hiệu
            brand_link = product_soup.find('a', href=lambda x: x and '/thuong-hieu/' in x)
            if brand_link:
                # Tìm trong parent element của thương hiệu
                parent = brand_link.parent
                if parent:
                    parent_text = parent.get_text()
                    code_match = re.search(r'P\d{5,6}', parent_text)
                    if code_match:
                        product_code = code_match.group()

        # Phương pháp 3: Tìm trong toàn bộ text trang (fallback)
        if product_code == "Không có":
            page_text = product_soup.get_text()
            code_match = re.search(r'P\d{5,6}', page_text)
            if code_match:
                product_code = code_match.group()

        # Lấy thương hiệu từ link "Thương hiệu:"
        brand_link = product_soup.find('a', href=lambda x: x and '/thuong-hieu/' in x)
        if brand_link:
            brand = brand_link.text.strip().replace("Thương hiệu: ", "")

        # Lấy giá và đơn vị từ cấu trúc HTML cụ thể
        import re
        unit = "Không có"

        # Phương pháp 1: Tìm div có class chứa "text-xl font-bold text-primary-500"
        price_divs = product_soup.find_all('div', class_=lambda x: x and 'text-xl' in str(x) and 'font-bold' in str(x) and 'text-primary-500' in str(x))

        found_price_unit = False
        for price_div in price_divs:
            div_text = price_div.get_text(strip=True)
            # Pattern: số.số₫/đơn_vị hoặc số.số ₫/đơn_vị
            price_unit_patterns = [
                r'(\d+[.,]?\d*)\s*₫\s*/\s*([A-Za-zÀ-ỹ]+)',  # 1.050.000₫/Cái
                r'(\d+[.,]?\d*)\s*₫\s*/\s*([A-Za-zÀ-ỹ]+)',  # với khoảng trắng
                r'(\d+[.,]?\d*)\xa0₫\s*/\s*([A-Za-zÀ-ỹ]+)'   # với &nbsp;
            ]

            for pattern in price_unit_patterns:
                match = re.search(pattern, div_text)
                if match:
                    price = match.group(1) + " ₫"
                    unit = match.group(2)
                    found_price_unit = True
                    break

            if found_price_unit:
                break

        # Phương pháp 2: Fallback - tìm từ text trên trang
        if not found_price_unit:
            page_text = product_soup.get_text()

            # Tìm giá có đơn vị cụ thể (ví dụ: "86.000 ₫/Kit")
            price_with_unit_patterns = [
                r'(\d+[.,]?\d*)\s*₫\s*/\s*(Kit|Hộp|Chai|Lọ|Vỉ|Gói|Bộ|Tuýp|Cái)',
                r'(\d+[.,]?\d*)\xa0₫\s*/\s*(Kit|Hộp|Chai|Lọ|Vỉ|Gói|Bộ|Tuýp|Cái)',
                r'(\d+[.,]?\d*)[\s\xa0]*₫\s*/\s*(Kit|Hộp|Chai|Lọ|Vỉ|Gói|Bộ|Tuýp|Cái)'
            ]

            for pattern in price_with_unit_patterns:
                price_with_unit_match = re.search(pattern, page_text)
                if price_with_unit_match:
                    price = price_with_unit_match.group(1) + " ₫"
                    unit = price_with_unit_match.group(2)
                    found_price_unit = True
                    break

        # Phương pháp 3: Nếu vẫn không tìm thấy, tìm giá thông thường
        if not found_price_unit:
            # Tìm trong div price trước
            for price_div in price_divs:
                div_text = price_div.get_text(strip=True)
                price_match = re.search(r'(\d+[.,]?\d*)\s*₫', div_text)
                if price_match:
                    price = price_match.group(0)
                    unit = "Không có"
                    found_price_unit = True
                    break

            # Fallback cuối: tìm trong toàn bộ text
            if not found_price_unit:
                page_text = product_soup.get_text()
                price_match = re.search(r'(\d+[.,]?\d*)\s*₫', page_text)
                if price_match:
                    price = price_match.group(0)
                    unit = "Không có"

        # Lấy danh mục từ cấu trúc HTML cụ thể
        # Tìm thẻ p có text "Danh mục" và lấy div ngay dưới nó
        danh_muc_elements = product_soup.find_all('p', string=lambda text: text and 'Danh mục' in text)

        for dm_p in danh_muc_elements:
            # Tìm div ngay sau thẻ p này
            next_div = dm_p.find_next_sibling('div')
            if next_div:
                category_text = next_div.get_text(strip=True)
                if category_text and len(category_text) > 2:  # Đảm bảo có nội dung thực sự
                    category = category_text
                    break

        # Fallback: Tìm từ breadcrumb nếu không tìm thấy bằng cách trên
        if category == "Không có":
            breadcrumb_links = product_soup.find_all('a', href=True)
            for link in breadcrumb_links:
                href = link.get('href', '')
                if '/danh-muc/' in href or '/category/' in href:
                    category = link.get_text(strip=True)
                    if category and len(category) > 2:
                        break

        # Fallback cuối: Tìm từ patterns trong text
        if category == "Không có":
            page_text = product_soup.get_text()
            if "Thiết bị y tế" in page_text:
                category = "Thiết bị y tế"
            elif "Dầu, Cao xoa bóp" in page_text:
                category = "Dầu, Cao xoa bóp"
            elif "Thuốc không kê đơn" in page_text:
                category = "Thuốc không kê đơn"

        # Lấy nhà sản xuất từ cấu trúc p + div
        # Tìm thẻ p có text "Nhà sản xuất" và lấy div ngay dưới nó
        nha_san_xuat_elements = product_soup.find_all('p', string=lambda text: text and 'Nhà sản xuất' in text)

        for nsx_p in nha_san_xuat_elements:
            # Tìm div ngay sau thẻ p này
            next_div = nsx_p.find_next_sibling('div')
            if next_div:
                manufacturer_text = next_div.get_text(strip=True)
                if manufacturer_text and len(manufacturer_text) > 2:  # Đảm bảo có nội dung thực sự
                    manufacturer = manufacturer_text
                    break

        # Lấy số đăng ký từ cấu trúc p + div
        # Tìm thẻ p có text "Số đăng ký" và lấy div ngay dưới nó
        so_dang_ky_elements = product_soup.find_all('p', string=lambda text: text and 'Số đăng ký' in text)

        for sdk_p in so_dang_ky_elements:
            # Tìm div ngay sau thẻ p này
            next_div = sdk_p.find_next_sibling('div')
            if next_div:
                reg_text = next_div.get_text(strip=True)
                if reg_text and len(reg_text) > 3:  # Đảm bảo có nội dung thực sự
                    registration_number = reg_text
                    break

        # Lấy phân loại sản phẩm từ cấu trúc label + div với button
        # Tìm thẻ label có text "Phân loại sản phẩm"
        classification_labels = product_soup.find_all('label', string=lambda text: text and 'Phân loại sản phẩm' in text)

        classification_list = []
        for label in classification_labels:
            # Tìm div chứa các button phân loại (thường có class "flex flex-wrap gap-2")
            parent_div = label.parent
            if parent_div:
                # Tìm div chứa các button
                button_container = parent_div.find('div', class_=lambda x: x and 'flex' in str(x) and 'gap' in str(x))
                if button_container:
                    # Lấy text từ tất cả các button trong container
                    buttons = button_container.find_all('button')
                    for button in buttons:
                        span = button.find('span')
                        if span:
                            classification_text = span.get_text(strip=True)
                            if classification_text and classification_text not in classification_list:
                                classification_list.append(classification_text)

        # Nối các phân loại bằng dấu phẩy
        if classification_list:
            product_classification = ", ".join(classification_list)

        # Fallback: Tìm từ cấu trúc p + div cũ
        if product_classification == "Không có":
            phan_loai_elements = product_soup.find_all('p', string=lambda text: text and 'Phân loại sản phẩm' in text)

            for pl_p in phan_loai_elements:
                # Tìm div ngay sau thẻ p này
                next_div = pl_p.find_next_sibling('div')
                if next_div:
                    classification_text = next_div.get_text(strip=True)
                    if classification_text and len(classification_text) > 3:  # Đảm bảo có nội dung thực sự
                        product_classification = classification_text
                        break

        # Lấy hoạt chất từ cấu trúc p + div
        # Tìm thẻ p có text "Hoạt chất" và lấy div ngay dưới nó
        hoat_chat_elements = product_soup.find_all('p', string=lambda text: text and 'Hoạt chất' in text)

        for hc_p in hoat_chat_elements:
            # Tìm div ngay sau thẻ p này
            next_div = hc_p.find_next_sibling('div')
            if next_div:
                ingredient_text = next_div.get_text(strip=True)
                if ingredient_text and len(ingredient_text) > 3:  # Đảm bảo có nội dung thực sự
                    # Làm sạch text
                    ingredient_text = re.sub(r'\s+', ' ', ingredient_text)  # Thay nhiều khoảng trắng bằng 1
                    active_ingredient = ingredient_text
                    break

        # Lấy chỉ định từ cấu trúc p + div
        # Tìm thẻ p có text "Chỉ định" và lấy div ngay dưới nó
        chi_dinh_elements = product_soup.find_all('p', string=lambda text: text and 'Chỉ định' in text)

        for cd_p in chi_dinh_elements:
            # Tìm div ngay sau thẻ p này
            next_div = cd_p.find_next_sibling('div')
            if next_div:
                indication_text = next_div.get_text(strip=True)
                if indication_text and len(indication_text) > 5:  # Đảm bảo có nội dung thực sự
                    # Làm sạch text và giới hạn độ dài
                    indication_text = re.sub(r'\s+', ' ', indication_text)  # Thay nhiều khoảng trắng bằng 1
                    if len(indication_text) > 500:  # Giới hạn 500 ký tự
                        indication_text = indication_text[:500] + "..."
                    indication = indication_text
                    break

        # Lấy đối tượng sử dụng từ cấu trúc p + div
        # Tìm thẻ p có text "Đối tượng sử dụng" và lấy div ngay dưới nó
        doi_tuong_elements = product_soup.find_all('p', string=lambda text: text and 'Đối tượng sử dụng' in text)

        for dt_p in doi_tuong_elements:
            # Tìm div ngay sau thẻ p này
            next_div = dt_p.find_next_sibling('div')
            if next_div:
                target_text = next_div.get_text(strip=True)
                if target_text and len(target_text) > 3:  # Đảm bảo có nội dung thực sự
                    # Làm sạch text
                    target_text = re.sub(r'\s+', ' ', target_text)  # Thay nhiều khoảng trắng bằng 1
                    target_user = target_text
                    break

        # Lấy dạng bào chế từ cấu trúc p + div
        # Tìm thẻ p có text "Dạng bào chế" và lấy div ngay dưới nó
        dang_bao_che_elements = product_soup.find_all('p', string=lambda text: text and 'Dạng bào chế' in text)

        for dbc_p in dang_bao_che_elements:
            # Tìm div ngay sau thẻ p này
            next_div = dbc_p.find_next_sibling('div')
            if next_div:
                dosage_text = next_div.get_text(strip=True)
                if dosage_text and len(dosage_text) > 3:  # Đảm bảo có nội dung thực sự
                    # Làm sạch text
                    dosage_text = re.sub(r'\s+', ' ', dosage_text)  # Thay nhiều khoảng trắng bằng 1
                    dosage_form = dosage_text
                    break

        # Lấy quy cách từ cấu trúc p + div
        # Tìm thẻ p có text "Quy cách" và lấy div ngay dưới nó
        quy_cach_elements = product_soup.find_all('p', string=lambda text: text and 'Quy cách' in text)

        for quy_cach_p in quy_cach_elements:
            # Tìm div ngay sau thẻ p này
            next_div = quy_cach_p.find_next_sibling('div')
            if next_div:
                spec_text = next_div.get_text(strip=True)
                if spec_text and len(spec_text) > 3:  # Đảm bảo có nội dung thực sự
                    # Làm sạch text
                    spec_text = re.sub(r'\s+', ' ', spec_text)  # Thay nhiều khoảng trắng bằng 1
                    specification = spec_text
                    break

        # Lấy cách dùng từ cấu trúc p + div
        # Tìm thẻ p có text "Cách dùng" và lấy div ngay dưới nó
        cach_dung_elements = product_soup.find_all('p', string=lambda text: text and 'Cách dùng' in text)

        for cd_p in cach_dung_elements:
            # Tìm div ngay sau thẻ p này
            next_div = cd_p.find_next_sibling('div')
            if next_div:
                usage_text = next_div.get_text(strip=True)
                if usage_text and len(usage_text) > 10:  # Đảm bảo có nội dung thực sự
                    # Làm sạch text và giới hạn độ dài
                    usage_text = re.sub(r'\s+', ' ', usage_text)  # Thay nhiều khoảng trắng bằng 1
                    if len(usage_text) > 500:  # Giới hạn 500 ký tự
                        usage_text = usage_text[:500] + "..."
                    usage = usage_text
                    break

        # Tìm lưu ý - tìm thẻ p có text "Lưu ý" và lấy div ngay dưới nó
        notes = "Không có"

        # Tìm thẻ p chứa text "Lưu ý"
        luu_y_elements = product_soup.find_all('p', string=lambda text: text and 'Lưu ý' in text)

        for luu_y_p in luu_y_elements:
            # Tìm div ngay sau thẻ p này
            next_div = luu_y_p.find_next_sibling('div')
            if next_div:
                notes_text = next_div.get_text(strip=True)
                if notes_text and len(notes_text) > 10:  # Đảm bảo có nội dung thực sự
                    # Làm sạch text và giới hạn độ dài
                    notes_text = re.sub(r'\s+', ' ', notes_text)  # Thay nhiều khoảng trắng bằng 1
                    if len(notes_text) > 500:  # Giới hạn 500 ký tự
                        notes_text = notes_text[:500] + "..."
                    notes = notes_text
                    break

        # Nếu không tìm thấy bằng cách trên, thử tìm bằng class hoặc cấu trúc khác
        if notes == "Không có":
            # Tìm theo cấu trúc grid với text "Lưu ý"
            grid_elements = product_soup.find_all('div', class_=lambda x: x and 'grid' in x)
            for grid in grid_elements:
                if 'Lưu ý' in grid.get_text():
                    # Tìm div chứa nội dung lưu ý trong grid này
                    content_divs = grid.find_all('div')
                    for div in content_divs:
                        div_text = div.get_text(strip=True)
                        if div_text and len(div_text) > 20 and 'Lưu ý' not in div_text:
                            notes_text = re.sub(r'\s+', ' ', div_text)
                            if len(notes_text) > 500:
                                notes_text = notes_text[:500] + "..."
                            notes = notes_text
                            break
                    if notes != "Không có":
                        break

        # Xử lý thẻ pmc-content-html để lấy cột động
        pmc_elements = product_soup.find_all(class_="pmc-content-html")

        if len(pmc_elements) == 1:
            # TH1: Chỉ có 1 thẻ pmc-content-html → lấy toàn bộ vào cột "Tổng hợp"
            tong_hop = pmc_elements[0].get_text(strip=True)
            if len(tong_hop) > 1000:  # Giới hạn độ dài
                tong_hop = tong_hop[:1000] + "..."

        elif len(pmc_elements) > 1:
            # TH2: Có nhiều thẻ → lấy ID thẻ cha làm tên cột, text trừ h(x) làm nội dung
            for element in pmc_elements:
                # Tìm thẻ cha có ID của element pmc-content-html này
                parent_element = element.parent
                column_name = None

                # Tìm ID từ thẻ cha hoặc các thẻ cha phía trên
                current_element = parent_element
                while current_element and column_name is None:
                    if current_element.get('id'):
                        column_name = current_element.get('id')
                        break
                    current_element = current_element.parent

                # Nếu không tìm thấy ID, thử lấy từ thẻ h(x) như cũ (fallback)
                if not column_name:
                    h_tags = element.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                    if h_tags:
                        column_name = h_tags[0].get_text(strip=True)

                if column_name:
                    # Lấy toàn bộ text của element
                    full_text = element.get_text(strip=True)

                    # Loại bỏ text của tất cả thẻ h(x) khỏi nội dung
                    content_text = full_text
                    h_tags = element.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                    for h_tag in h_tags:
                        h_text = h_tag.get_text(strip=True)
                        content_text = content_text.replace(h_text, "", 1)  # Chỉ replace lần đầu tiên

                    # Làm sạch text
                    content_text = content_text.strip()
                    content_text = re.sub(r'\s+', ' ', content_text)  # Thay nhiều khoảng trắng bằng 1

                    if len(content_text) > 1000:  # Giới hạn độ dài
                        content_text = content_text[:1000] + "..."

                    # Lưu vào dictionary với ID thẻ cha làm key
                    if column_name and content_text:
                        pmc_columns[column_name] = content_text

        # Lấy ảnh sản phẩm
        product_images_list = get_product_images(driver, url)
        if product_images_list:
            product_images = "\n".join(product_images_list)
        else:
            product_images = "Không có"

        print(f"✅ Tên: {product_name[:50]}...")
        print(f"✅ Giá: {price}")
        print(f"✅ Danh mục: {category}")
        print(f"✅ Nhà sản xuất: {manufacturer}")
        print(f"✅ Ảnh: {len(product_images_list) if product_images_list else 0} ảnh")
        print(f"✅ PMC elements: {len(pmc_elements)} thẻ")

        # Gom dữ liệu cơ bản (18 cột đầu)
        base_data = [
            url,                   # 1. URL
            product_name,          # 2. Tên sản phẩm
            product_code,          # 3. Mã sản phẩm
            brand,                 # 4. Thương hiệu
            price,                 # 5. Giá
            unit,                  # 6. Đơn vị
            category,              # 7. Danh mục
            manufacturer,          # 8. Nhà sản xuất
            registration_number,   # 9. Số đăng ký
            product_classification,# 10. Phân loại sản phẩm
            active_ingredient,     # 11. Hoạt chất
            indication,            # 12. Chỉ định
            target_user,           # 13. Đối tượng sử dụng
            dosage_form,           # 14. Dạng bào chế
            specification,         # 15. Quy cách
            usage,                 # 16. Cách dùng
            notes,                 # 17. Lưu ý
            product_images,        # 18. Ảnh sản phẩm
        ]

        # Thêm cột "Tổng hợp" vào vị trí 19 (chỉ cho TH1)
        base_data.append(tong_hop)

        # Ghi data cố định trước (19 cột: 18 cột cơ bản + "Tổng hợp")
        try:
            sheet.update(f"A{row}:S{row}", [base_data])
            print(f"✅ Đã ghi data cố định: {product_name}")
            time.sleep(1)  # Chờ 1 giây sau mỗi lần ghi để tránh quota exceeded
        except Exception as e:
            if "quota exceeded" in str(e).lower() or "429" in str(e):
                print(f"⚠️ Quota exceeded, chờ 60 giây...")
                time.sleep(60)
                # Thử lại
                sheet.update(f"A{row}:S{row}", [base_data])
                print(f"✅ Đã ghi data cố định (retry): {product_name}")
            else:
                raise e

        # Xử lý các cột động từ pmc-content-html
        if pmc_columns:
            print(f"🔄 Xử lý {len(pmc_columns)} cột động...")

            for column_name, content in pmc_columns.items():
                # Kiểm tra xem cột này đã tồn tại chưa
                if column_name not in dynamic_columns:
                    # Tạo cột mới
                    next_column_index = len(fixed_header) + len(dynamic_columns)  # 19 + số cột động hiện có
                    dynamic_columns[column_name] = next_column_index

                    # Tính tên cột (T, U, V, ..., Z, AA, AB, ...)
                    column_letter = get_column_letter(next_column_index)

                    # Ghi header cho cột mới
                    try:
                        sheet.update(f"{column_letter}1", [[column_name]])
                        print(f"   ➕ Tạo cột mới '{column_name}' tại {column_letter}")
                        time.sleep(1)  # Chờ 1 giây sau mỗi lần ghi header
                    except Exception as e:
                        if "quota exceeded" in str(e).lower() or "429" in str(e):
                            print(f"⚠️ Quota exceeded khi tạo header, chờ 60 giây...")
                            time.sleep(60)
                            sheet.update(f"{column_letter}1", [[column_name]])
                            print(f"   ➕ Đã tạo cột mới (retry): '{column_name}' tại {column_letter}")
                        else:
                            raise e

                # Ghi data vào cột
                column_index = dynamic_columns[column_name]
                column_letter = get_column_letter(column_index)

                try:
                    sheet.update(f"{column_letter}{row}", [[content]])
                    print(f"   📝 Ghi data vào cột '{column_name}' ({column_letter})")
                    time.sleep(1)  # Chờ 1 giây sau mỗi lần ghi data
                except Exception as e:
                    if "quota exceeded" in str(e).lower() or "429" in str(e):
                        print(f"⚠️ Quota exceeded khi ghi data, chờ 60 giây...")
                        time.sleep(60)
                        sheet.update(f"{column_letter}{row}", [[content]])
                        print(f"   📝 Đã ghi data (retry) vào cột '{column_name}' ({column_letter})")
                    else:
                        raise e

        return True

    except Exception as e:
        print(f"❌ Lỗi khi lấy thông tin từ {url}: {e}")
        return False

def main():
    # Khởi động trình duyệt
    print("Đang khởi tạo Chrome browser...")
    service = Service(ChromeDriverManager().install())
    
    # Cấu hình Chrome options
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    
    # Tắt logging để giảm lỗi console
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--log-level=3")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    print("✅ Chrome browser đã sẵn sàng!")
    
    # Kết nối Google Sheets
    print("Đang kết nối Google Sheets...")
    sheet = connect_to_google_sheets()
    print("✅ Đã kết nối Google Sheets (MedicalDevice)!")
    
    try:
        # Truy cập trang thiết bị y tế
        print(f"🔍 Đang truy cập: {BASE_URL}")
        driver.get(BASE_URL)
        time.sleep(5)  # Chờ trang load
        
        # Bấm nút "Xem thêm" cho đến khi hết
        click_count = click_load_more_buttons(driver)
        
        # Lấy tất cả link sản phẩm
        product_links = get_product_links(driver)
        
        if not product_links:
            print("❌ Không tìm thấy link sản phẩm nào!")
            return
        
        # Giới hạn số sản phẩm nếu cần
        if len(product_links) > MAX_PRODUCTS:
            product_links = product_links[:MAX_PRODUCTS]
            print(f"⚠️ Giới hạn chỉ lấy {MAX_PRODUCTS} sản phẩm đầu tiên")
        
        print(f"📋 Sẽ crawl {len(product_links)} sản phẩm")

        # Đảm bảo sheet có ít nhất 50 cột để tránh lỗi
        ensure_sheet_columns(sheet, 50)

        # Tạo header cố định trước (19 cột: 18 cột cơ bản + "Tổng hợp") - chỉ khi bắt đầu từ đầu
        fixed_header = ["URL", "Tên sản phẩm", "Mã sản phẩm", "Thương hiệu", "Giá", "Đơn vị", "Danh mục", "Nhà sản xuất", "Số đăng ký", "Phân loại sản phẩm", "Hoạt chất", "Chỉ định", "Đối tượng sử dụng", "Dạng bào chế", "Quy cách", "Cách dùng", "Lưu ý", "Ảnh sản phẩm", "Tổng hợp"]

        if START_ROW <= 2:
            # Chỉ tạo header khi bắt đầu từ đầu
            sheet.update("A1:S1", [fixed_header])
            print(f"✅ Đã tạo header cố định với {len(fixed_header)} cột")
            row = 2  # Bắt đầu ghi data từ hàng 2
        else:
            # Tiếp tục từ dòng đã chỉ định
            row = START_ROW
            print(f"🔄 Tiếp tục crawl từ dòng {START_ROW}")

        # Lấy thông tin các cột động đã có (nếu tiếp tục crawl)
        dynamic_columns = {}  # Dictionary để track các cột động đã tạo {column_name: column_index}

        if START_ROW > 2:
            # Đọc header hiện có để biết các cột động đã tồn tại
            try:
                existing_headers = sheet.row_values(1)
                for i, header in enumerate(existing_headers):
                    if i >= len(fixed_header):  # Các cột sau cột 19 là cột động
                        dynamic_columns[header] = i
                print(f"✅ Đã load {len(dynamic_columns)} cột động có sẵn: {list(dynamic_columns.keys())}")
            except Exception as e:
                print(f"⚠️ Không thể đọc header hiện có: {e}")
                print("🔄 Sẽ tạo cột động mới khi cần")

        # Crawl từng sản phẩm
        products_to_skip = START_ROW - 2 if START_ROW > 2 else 0  # Số sản phẩm cần skip

        print(f"🔧 DEBUG: START_ROW = {START_ROW}")
        print(f"🔧 DEBUG: products_to_skip = {products_to_skip}")
        print(f"🔧 DEBUG: row = {row}")

        for i, product_url in enumerate(product_links, 1):
            # Skip các sản phẩm đã crawl
            if i <= products_to_skip:
                print(f"⏭️ Skip sản phẩm {i}/{len(product_links)} (đã crawl)")
                continue

            print(f"\n📋 Sản phẩm {i}/{len(product_links)} (dòng {row})")

            # Lấy thông tin sản phẩm và ghi trực tiếp vào sheet
            success = get_product_info(driver, product_url, sheet, row, dynamic_columns, fixed_header)

            if success:
                row += 1

            # Chờ 2 giây giữa các request để tránh bị block và quota exceeded
            time.sleep(2)

        crawled_count = len(product_links) - products_to_skip
        print(f"✅ Đã crawl và ghi {crawled_count} sản phẩm mới vào sheet MedicalDevice!")
        print(f"📊 Tổng cộng đã có {row - 1} dòng data trong sheet")
        
    except Exception as e:
        print(f"❌ Lỗi trong quá trình crawl: {e}")
    
    finally:
        # Đóng trình duyệt
        driver.quit()
        print("\n🎉 Hoàn tất quá trình crawl thiết bị y tế!")

if __name__ == "__main__":
    main()
