import pymysql
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import time
import sys
import os
import requests
import uuid
import random
from urllib.parse import urlparse

# ===== CẤU HÌNH =====
# Google Sheets config
SHEET_ID = "1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8"
SHEET_NAME = "MomAndBaby"  # Sheet MomAndBaby
CREDENTIALS_FILE = "medical-crawl-2024-013b6faaa588.json"

# MySQL config cho Docker Adminer
DB_CONFIG = {
    'host': 'localhost',     # Hoặc thử 'mysql' nếu localhost không được
    'port': 3307,            # Port Docker MySQL (thường là 3307)
    'user': 'sail',          # Username từ Adminer
    'password': 'password',  # Password từ Adminer
    'database': 'bagisto',   # Database từ Adminer
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# ===== CẤU HÌNH TEST =====
ROW_NUMBER = 1  # 🎯 KIỂM SOÁT SỐ HÀNG: Số dòng tối đa muốn xử lý (None = tất cả dòng)
START_ROW = 2  # Bắt đầu từ dòng nào (2 = bỏ qua header)

# Cấu hình ảnh cho product_images
LOCAL_IMAGE_PATH = r"C:\Working\Medical-EC\MEDICAL_EC_SYSTEM\storage\app\public\EC-Images"
IMAGE_URL_PREFIX = "EC-Images/"  # Prefix để lưu vào DB

# ===== MAPPING CỘT GOOGLE SHEETS =====
# Mapping cột cho MomAndBaby (LOẠI BỎ: hoạt chất, chỉ định, dạng bào chế, nhà sản xuất)
COLUMN_MAPPING = {
    'url': 0,                    # Cột A - URL
    'name': 1,                   # Cột B - Tên sản phẩm
    'sku': 2,                    # Cột C - Mã sản phẩm (SKU)
    'brand': 3,                  # Cột D - Thương hiệu
    'price': 4,                  # Cột E - Giá
    'unit': 5,                   # Cột F - Đơn vị
    'category': 6,               # Cột G - Danh mục
    # 'manufacturer': 7,         # Cột H - Nhà sản xuất (LOẠI BỎ)
    'registration_number': 8,    # Cột I - Số đăng ký
    'product_classification': 9, # Cột J - Phân loại sản phẩm
    # 'active_ingredient': 10,   # Cột K - Hoạt chất (LOẠI BỎ)
    # 'indication': 11,          # Cột L - Chỉ định (LOẠI BỎ)
    'target_user': 12,           # Cột M - Đối tượng sử dụng
    # 'dosage_form': 13,         # Cột N - Dạng bào chế (LOẠI BỎ)
    'specification': 14,         # Cột O - Quy cách
    'usage': 15,                 # Cột P - Cách dùng
    'note': 16,                  # Cột Q - Lưu ý
    'images': 17,                # Cột R - Ảnh sản phẩm
    'summary': 18,               # Cột S - Tổng hợp
}

# ===== CẤU HÌNH CÁC BẢNG CẦN INSERT =====
TABLES_TO_INSERT = [
    "products",
    "product_flat",
    "product_attribute_values",
    "product_categories",
    "product_images",
    "product_inventories"
]

# ===== HELPER FUNCTIONS =====

def connect_to_google_sheets():
    """Kết nối Google Sheets"""
    try:
        scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
        creds = ServiceAccountCredentials.from_json_keyfile_name(CREDENTIALS_FILE, scope)
        client = gspread.authorize(creds)
        sheet = client.open_by_key(SHEET_ID).worksheet(SHEET_NAME)
        print(f"✅ Kết nối Google Sheets thành công: {SHEET_NAME}")
        return sheet
    except Exception as e:
        print(f"❌ Lỗi kết nối Google Sheets: {e}")
        sys.exit(1)

def connect_to_mysql():
    """Kết nối MySQL Docker"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print(f"✅ Kết nối Docker MySQL thành công: {DB_CONFIG['database']}")
        return connection
    except Exception as e:
        print(f"❌ Lỗi kết nối Docker MySQL: {e}")
        sys.exit(1)

def get_column_value(row, column_name):
    """Lấy giá trị từ cột theo tên"""
    try:
        if column_name not in COLUMN_MAPPING:
            return None
        
        col_index = COLUMN_MAPPING[column_name]
        if col_index < len(row):
            value = row[col_index]
            return value.strip() if isinstance(value, str) and value.strip() else None
        return None
    except Exception as e:
        print(f"  ❌ Lỗi khi lấy giá trị cột '{column_name}': {e}")
        return None

def get_data_from_sheet(sheet):
    """Lấy dữ liệu từ Google Sheets"""
    try:
        print(f"📊 Đang lấy dữ liệu từ sheet '{SHEET_NAME}'...")
        
        # Lấy tất cả dữ liệu
        all_data = sheet.get_all_values()
        
        if not all_data:
            print("⚠️ Sheet trống!")
            return []
        
        # Bỏ qua header (dòng đầu tiên)
        data_rows = all_data[START_ROW-1:]  # START_ROW-1 vì index bắt đầu từ 0
        
        # Giới hạn số dòng nếu có cấu hình
        if ROW_NUMBER:
            data_rows = data_rows[:ROW_NUMBER]
        
        print(f"📋 Lấy được {len(data_rows)} dòng dữ liệu")
        return data_rows
        
    except Exception as e:
        print(f"❌ Lỗi khi lấy dữ liệu từ sheet: {e}")
        return []

def check_tables_exist(connection):
    """Kiểm tra các bảng cần thiết có tồn tại không"""
    try:
        with connection.cursor() as cursor:
            for table in TABLES_TO_INSERT:
                cursor.execute(f"SHOW TABLES LIKE '{table}'")
                result = cursor.fetchone()
                if result:
                    print(f"  ✅ Bảng '{table}' tồn tại")
                else:
                    print(f"  ❌ Bảng '{table}' KHÔNG tồn tại")
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra bảng: {e}")

def check_existing_data(connection):
    """Kiểm tra dữ liệu hiện có trong các bảng"""
    try:
        with connection.cursor() as cursor:
            for table in TABLES_TO_INSERT:
                cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                result = cursor.fetchone()
                count = result['count'] if result else 0
                print(f"  📊 Bảng '{table}': {count} records")
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra dữ liệu: {e}")

def check_sku_exists(connection, sku):
    """Kiểm tra SKU đã tồn tại chưa"""
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT id FROM products WHERE sku = %s", (sku,))
            result = cursor.fetchone()
            return result['id'] if result else None
    except Exception as e:
        print(f"  ❌ Lỗi khi kiểm tra SKU '{sku}': {e}")
        return None

def find_local_images_by_sku(sku):
    """Tìm ảnh local theo SKU"""
    try:
        if not os.path.exists(LOCAL_IMAGE_PATH):
            return []
        
        # Tìm file có tên chứa SKU
        matching_files = []
        for filename in os.listdir(LOCAL_IMAGE_PATH):
            if sku.lower() in filename.lower() and filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
                matching_files.append(filename)
        
        return sorted(matching_files)[:5]  # Tối đa 5 ảnh
        
    except Exception as e:
        print(f"  ❌ Lỗi khi tìm ảnh cho SKU '{sku}': {e}")
        return []

# ===== CATEGORY FUNCTIONS =====

def get_category_id_by_name(connection, category_name):
    """Lấy category_id từ tên category"""
    try:
        if not category_name or category_name == "Không có":
            return 1  # Default category
        
        with connection.cursor() as cursor:
            # Tìm category theo tên
            cursor.execute("SELECT id FROM categories WHERE name = %s", (category_name,))
            result = cursor.fetchone()
            
            if result:
                return result['id']
            else:
                # Tạo category mới nếu chưa tồn tại
                cursor.execute("""
                INSERT INTO categories (position, image, status, _lft, _rgt, parent_id, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
                """, (1, None, 1, 1, 2, 1))
                
                category_id = cursor.lastrowid
                
                # Insert vào category_translations
                cursor.execute("""
                INSERT INTO category_translations (name, slug, description, meta_title, meta_description, meta_keywords, category_id, locale)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (category_name, category_name.lower().replace(' ', '-'), category_name, category_name, category_name, category_name, category_id, 'en'))
                
                print(f"      ✅ Tạo category mới: '{category_name}' (ID: {category_id})")
                return category_id
                
    except Exception as e:
        print(f"  ❌ Lỗi khi lấy category_id cho '{category_name}': {e}")
        return 1  # Default category

# ===== UNIT TYPE FUNCTIONS =====

def get_unit_type_id_by_name(connection, unit_type_name):
    """Lấy unit_type_id từ tên unit_type (đơn vị)"""
    try:
        if not unit_type_name or unit_type_name == "Không có":
            return None
        
        with connection.cursor() as cursor:
            # Tìm unit_type theo tên
            cursor.execute("SELECT id FROM unit_types WHERE name = %s", (unit_type_name,))
            result = cursor.fetchone()
            
            if result:
                return result['id']
            else:
                # Tạo unit_type mới nếu chưa tồn tại
                cursor.execute("""
                INSERT INTO unit_types (name, created_at, updated_at)
                VALUES (%s, NOW(), NOW())
                """, (unit_type_name,))
                
                unit_type_id = cursor.lastrowid
                print(f"      ✅ Tạo unit_type mới: '{unit_type_name}' (ID: {unit_type_id})")
                return unit_type_id
                
    except Exception as e:
        print(f"  ❌ Lỗi khi lấy unit_type_id cho '{unit_type_name}': {e}")
        return None

# ===== DATA PROCESSORS CHO MOM & BABY PRODUCTS =====

def process_mom_baby_data(connection, row):
    """Xử lý dữ liệu cho 1 sản phẩm Mẹ và bé"""
    # Lấy dữ liệu cơ bản
    name = get_column_value(row, 'name')
    sku = get_column_value(row, 'sku')

    # Kiểm tra dữ liệu hợp lệ
    if not name or not sku:
        return None

    print(f"    📦 Xử lý sản phẩm Mẹ và bé: '{name}' (SKU: {sku})")

    # Tạo dữ liệu cho products table
    product_data = {
        'sku': sku,
        'type': 'simple',  # Mặc định simple
        'attribute_family_id': 1,  # Mặc định 1
        'parent_id': None,
        'created_at': 'NOW()',
        'updated_at': 'NOW()'
    }

    # Lấy thêm thông tin
    brand = get_column_value(row, 'brand')
    price = get_column_value(row, 'price')
    unit = get_column_value(row, 'unit')
    category = get_column_value(row, 'category')

    # Parse price thành float nếu có
    parsed_price = None
    if price and price != "Không có":
        try:
            # Loại bỏ ký tự không phải số
            price_clean = ''.join(c for c in str(price) if c.isdigit() or c == '.')
            parsed_price = float(price_clean) if price_clean else None
        except:
            parsed_price = None

    # Tạo dữ liệu cho product_flat table
    product_flat_data = {
        'sku': sku,
        'name': name,
        'description': get_column_value(row, 'summary') or f"Sản phẩm {name}",
        'short_description': get_column_value(row, 'note') or f"Sản phẩm {name} chất lượng cao",
        'url_key': sku.lower().replace(' ', '-'),
        'new': 1,
        'featured': 0,
        'status': 1,
        'visible_individually': 1,
        'thumbnail': None,
        'price': parsed_price,
        'cost': None,
        'special_price': None,
        'special_price_from': None,
        'special_price_to': None,
        'weight': 1.0,
        'color': None,
        'color_label': None,
        'size': None,
        'size_label': None,
        'brand': brand,
        'guest_checkout': 1,
        'brand_label': brand,
        'meta_title': name,
        'meta_keywords': f"{name}, {brand}" if brand else name,
        'meta_description': f"Mua {name} chính hãng, giá tốt",
        'width': None,
        'height': None,
        'depth': None,
        'product_id': None,  # Sẽ được set sau khi insert products
        'parent_id': None,
        'locale': 'en',
        'channel': 'default',
        'attribute_family_id': 1,
        'created_at': 'NOW()',
        'updated_at': 'NOW()'
    }

    return {
        'product_data': product_data,
        'product_flat_data': product_flat_data,
        'category': category,
        'unit': unit
    }

def insert_mom_baby_basic(connection, processed_data):
    """Insert vào products và product_flat tables"""
    try:
        with connection.cursor() as cursor:
            # Bước 1: Insert vào products
            product_data = processed_data['product_data']
            product_columns = list(product_data.keys())
            product_values = list(product_data.values())

            # Xử lý NOW() cho timestamps
            product_sql = f"INSERT INTO products ({', '.join(product_columns)}) VALUES ({', '.join(['NOW()' if v == 'NOW()' else '%s' for v in product_values])})"
            product_values_filtered = [v for v in product_values if v != 'NOW()']
            cursor.execute(product_sql, product_values_filtered)

            # Lấy product_id vừa insert
            product_id = cursor.lastrowid
            print(f"      ✅ Inserted products: ID={product_id}, SKU='{product_data['sku']}'")

            # Bước 2: Insert vào product_flat
            product_flat_data = processed_data['product_flat_data']
            product_flat_data['product_id'] = product_id  # Set product_id

            flat_columns = list(product_flat_data.keys())
            flat_values = list(product_flat_data.values())

            # Xử lý NOW() cho timestamps
            flat_sql = f"INSERT INTO product_flat ({', '.join(flat_columns)}) VALUES ({', '.join(['NOW()' if v == 'NOW()' else '%s' for v in flat_values])})"
            flat_values_filtered = [v for v in flat_values if v != 'NOW()']
            cursor.execute(flat_sql, flat_values_filtered)

            print(f"      ✅ Inserted product_flat: product_id={product_id}, name='{product_flat_data['name']}'")

            return product_id

    except Exception as e:
        print(f"      ❌ Lỗi khi insert mom baby basic: {e}")
        raise e

def insert_mom_baby_attributes(connection, product_id, row):
    """Insert các attributes cho sản phẩm Mẹ và bé (LOẠI BỎ: hoạt chất, chỉ định, dạng bào chế, nhà sản xuất)"""
    try:
        with connection.cursor() as cursor:
            # Danh sách attributes cần insert (đã loại bỏ các cột không cần)
            attributes_to_insert = [
                ('brand', 'brand', 'text'),
                ('registration_number', 'registration_number', 'text'),
                ('product_classification', 'product_classification', 'text'),
                ('target_user', 'target_user', 'text'),
                ('specification', 'specification', 'textarea'),
                ('usage', 'usage', 'textarea'),
                ('note', 'note', 'textarea'),
                ('summary', 'summary', 'textarea'),
            ]

            for column_name, attribute_code, attribute_type in attributes_to_insert:
                value = get_column_value(row, column_name)

                if value and value != "Không có":
                    # Lấy attribute_id
                    cursor.execute("SELECT id FROM attributes WHERE code = %s", (attribute_code,))
                    attr_result = cursor.fetchone()

                    if attr_result:
                        attribute_id = attr_result['id']
                        unique_id = f"{product_id}|{attribute_id}|en|default"

                        # Insert vào product_attribute_values
                        if attribute_type == 'text':
                            sql = """
                            INSERT INTO product_attribute_values (locale, channel, text_value, product_id, attribute_id, unique_id)
                            VALUES (%s, %s, %s, %s, %s, %s)
                            """
                            cursor.execute(sql, ('en', 'default', value, product_id, attribute_id, unique_id))
                        else:  # textarea
                            sql = """
                            INSERT INTO product_attribute_values (locale, channel, text_value, product_id, attribute_id, unique_id)
                            VALUES (%s, %s, %s, %s, %s, %s)
                            """
                            cursor.execute(sql, ('en', 'default', value, product_id, attribute_id, unique_id))

                        print(f"      ✅ Inserted attribute: {attribute_code} = '{value[:50]}{'...' if len(value) > 50 else ''}'")

    except Exception as e:
        print(f"      ❌ Lỗi khi insert mom baby attributes: {e}")
        raise e

def insert_mom_baby_categories(connection, product_id, row):
    """Insert vào product_categories table"""
    try:
        category_name = get_column_value(row, 'category')
        category_id = get_category_id_by_name(connection, category_name)

        with connection.cursor() as cursor:
            # Insert vào product_categories
            sql = "INSERT INTO product_categories (product_id, category_id) VALUES (%s, %s)"
            cursor.execute(sql, (product_id, category_id))

            print(f"      ✅ Inserted product category: product_id={product_id}, category='{category_name}' (ID: {category_id})")

    except Exception as e:
        print(f"      ❌ Lỗi khi insert mom baby categories: {e}")
        raise e

def insert_mom_baby_images(connection, product_id, sku):
    """Insert vào product_images table từ ảnh local"""
    try:
        # Tìm ảnh local theo SKU
        local_images = find_local_images_by_sku(sku)

        if not local_images:
            print(f"      ⚠️ Không tìm thấy ảnh local cho SKU '{sku}'")
            return

        with connection.cursor() as cursor:
            inserted_count = 0

            # Insert từng ảnh (tối đa 5 ảnh)
            for i, filename in enumerate(local_images[:5], start=1):
                image_path = f"{IMAGE_URL_PREFIX}{filename}"

                # Insert vào product_images
                img_sql = "INSERT INTO product_images (product_id, path, position) VALUES (%s, %s, %s)"
                cursor.execute(img_sql, (product_id, image_path, i))
                inserted_count += 1

            print(f"      ✅ Inserted {inserted_count} product images từ local")

    except Exception as e:
        print(f"      ❌ Lỗi khi insert mom baby images: {e}")
        raise e

def insert_mom_baby_inventories(connection, product_id):
    """Insert vào product_inventories table"""
    try:
        with connection.cursor() as cursor:
            # Insert vào product_inventories
            inv_sql = """
            INSERT INTO product_inventories (qty, product_id, vendor_id, inventory_source_id)
            VALUES (%s, %s, %s, %s)
            """
            cursor.execute(inv_sql, (100, product_id, 0, 1))

            print(f"      ✅ Inserted product inventory: qty=100, product_id={product_id}")

    except Exception as e:
        print(f"      ❌ Lỗi khi insert mom baby inventories: {e}")
        raise e

def insert_mom_baby_random_attribute(connection, product_id):
    """Insert random attribute cho sản phẩm"""
    try:
        with connection.cursor() as cursor:
            # Lấy random attribute (giả sử attribute_id = 23)
            attribute_id = 23
            random_value = random.randint(1, 100)
            unique_id = f"{product_id}|{attribute_id}|en|default"

            # Insert vào product_attribute_values
            sql = """
            INSERT INTO product_attribute_values (locale, channel, integer_value, product_id, attribute_id, unique_id)
            VALUES (%s, %s, %s, %s, %s, %s)
            """
            cursor.execute(sql, ('en', 'default', random_value, product_id, attribute_id, unique_id))

            print(f"      ✅ Inserted random attribute: product_id={product_id}, attribute_id={attribute_id}, value={random_value}")

    except Exception as e:
        print(f"      ❌ Lỗi khi insert random attribute: {e}")
        raise e

def insert_mom_baby_boolean_attribute(connection, product_id):
    """Insert boolean attribute cho sản phẩm"""
    try:
        with connection.cursor() as cursor:
            # Lấy boolean attribute (giả sử attribute_id = 24)
            attribute_id = 24
            boolean_value = random.choice([0, 1])
            unique_id = f"{product_id}|{attribute_id}|en|default"

            # Insert vào product_attribute_values
            sql = """
            INSERT INTO product_attribute_values (locale, channel, boolean_value, product_id, attribute_id, unique_id)
            VALUES (%s, %s, %s, %s, %s, %s)
            """
            cursor.execute(sql, ('en', 'default', boolean_value, product_id, attribute_id, unique_id))

            print(f"      ✅ Inserted boolean attribute: product_id={product_id}, attribute_id={attribute_id}, boolean_value={boolean_value}")

    except Exception as e:
        print(f"      ❌ Lỗi khi insert boolean attribute: {e}")
        raise e

def insert_mom_baby_workflow(connection, data_rows):
    """Insert workflow: products → product_flat → attributes → categories → images → inventories → random_attr → boolean_attr"""
    print("🚀 BẮT ĐẦU WORKFLOW INSERT MOM & BABY PRODUCTS (DOCKER)")
    print("📋 Workflow: products → product_flat → attributes → categories → images → inventories → random_attr → boolean_attr")
    print("⚠️ LOẠI BỎ: hoạt chất, chỉ định, dạng bào chế, nhà sản xuất")

    total_success = 0
    total_failed = 0
    total_skipped = 0
    processed_skus = set()  # Để tránh duplicate SKUs

    for i, row in enumerate(data_rows, start=START_ROW):
        try:
            print(f"\n📦 Xử lý dòng {i}/{len(data_rows) + START_ROW - 1}")

            # Bước 1: Kiểm tra dữ liệu cơ bản
            name = get_column_value(row, 'name')
            sku = get_column_value(row, 'sku')

            if not name or not sku:
                print(f"    ⚠️ Bỏ qua dòng {i}: Thiếu tên hoặc SKU")
                total_skipped += 1
                continue

            # Bước 2: Kiểm tra duplicate SKU trong batch hiện tại
            if sku in processed_skus:
                print(f"    ⚠️ Bỏ qua dòng {i}: SKU '{sku}' đã xử lý trong batch này")
                total_skipped += 1
                continue

            # Bước 3: Kiểm tra SKU đã tồn tại trong DB
            existing_id = check_sku_exists(connection, sku)
            if existing_id:
                print(f"    ⚠️ Bỏ qua dòng {i}: SKU '{sku}' đã tồn tại với ID={existing_id}")
                total_skipped += 1
                continue

            # Bước 4: Xử lý dữ liệu
            processed_data = process_mom_baby_data(connection, row)
            if not processed_data:
                print(f"    ❌ Lỗi xử lý dữ liệu dòng {i}")
                total_failed += 1
                continue

            # Commit transaction cho mỗi sản phẩm
            with connection.cursor() as cursor:
                try:
                    # Bắt đầu transaction
                    connection.begin()

                    processed_skus.add(sku)

                    # Bước 5: Insert products và product_flat
                    product_id = insert_mom_baby_basic(connection, processed_data)

                    # Bước 6: Insert product attributes (đã loại bỏ các cột không cần)
                    insert_mom_baby_attributes(connection, product_id, row)

                    # Bước 7: Insert product categories
                    insert_mom_baby_categories(connection, product_id, row)

                    # Bước 8: Insert product images từ local
                    insert_mom_baby_images(connection, product_id, sku)

                    # Bước 9: Insert product inventories
                    insert_mom_baby_inventories(connection, product_id)

                    # Bước 10: Insert random attribute
                    insert_mom_baby_random_attribute(connection, product_id)

                    # Bước 11: Insert boolean attribute
                    insert_mom_baby_boolean_attribute(connection, product_id)

                    # Commit transaction
                    connection.commit()
                    total_success += 1
                    print(f"    ✅ Hoàn thành sản phẩm: '{name}' (ID: {product_id})")

                except Exception as e:
                    # Rollback nếu có lỗi
                    connection.rollback()
                    print(f"    ❌ Lỗi transaction dòng {i}: {e}")
                    total_failed += 1
                    continue

        except Exception as e:
            print(f"    ❌ Lỗi tổng quát dòng {i}: {e}")
            total_failed += 1
            continue

        # Delay giữa các sản phẩm
        time.sleep(0.1)

    # Tổng kết
    print(f"\n" + "="*60)
    print(f"🎯 KẾT QUẢ INSERT MOM & BABY PRODUCTS:")
    print(f"✅ Thành công: {total_success}")
    print(f"❌ Thất bại: {total_failed}")
    print(f"⚠️ Bỏ qua: {total_skipped}")
    print(f"📊 Tổng cộng: {total_success + total_failed + total_skipped}")
    print("="*60)

def main():
    """Hàm main cho Mom & Baby Products workflow"""
    print("🚀 BẮT ĐẦU INSERT MOM & BABY PRODUCTS VÀO DOCKER MYSQL (ADMINER)")
    print("="*60)
    print(f"📊 Cấu hình: Insert tối đa {ROW_NUMBER if ROW_NUMBER else 'TẤT CẢ'} dòng")
    print(f"🐳 Docker Database: {DB_CONFIG['database']}")
    print(f"🖥️ Docker Host: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"📋 Active Workflow: Mom & Baby Products Multi-Tables")
    print(f"📋 Tables: {', '.join(TABLES_TO_INSERT)}")
    print(f"🖼️ Local Images Path: {LOCAL_IMAGE_PATH}")
    print(f"⚠️ LOẠI BỎ: hoạt chất, chỉ định, dạng bào chế, nhà sản xuất")
    print(f"🌐 Adminer URL: http://localhost:8080")
    print("="*60)

    try:
        # Kết nối Google Sheets
        sheet = connect_to_google_sheets()

        # Kết nối Docker MySQL
        connection = connect_to_mysql()

        # Kiểm tra tables trong Docker
        check_tables_exist(connection)

        # Kiểm tra dữ liệu hiện có
        check_existing_data(connection)

        # Lấy dữ liệu từ Google Sheets
        data_rows = get_data_from_sheet(sheet)

        # Insert dữ liệu vào Docker MySQL
        insert_mom_baby_workflow(connection, data_rows)

        # Kiểm tra lại sau khi insert
        print("\n🔍 Kiểm tra lại sau khi insert:")
        check_existing_data(connection)

        # Đóng kết nối
        connection.close()

        print("\n" + "="*60)
        print("🎉 HOÀN TẤT INSERT MOM & BABY PRODUCTS VÀO DOCKER!")
        print("🌐 Kiểm tra kết quả tại: http://localhost:8080")
        print("="*60)

    except KeyboardInterrupt:
        print("\n⚠️ Script bị dừng bởi người dùng (Ctrl+C)")
    except Exception as e:
        print(f"\n❌ Lỗi không mong muốn: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
