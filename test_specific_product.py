import requests
from bs4 import BeautifulSoup
import re

def test_specific_product_code():
    """Test với sản phẩm cụ thể có mã P17386"""
    
    # Thử với một số URL có thể có mã P17386
    test_urls = [
        "https://www.pharmacity.vn/vien-nang-mediplantex-khong-co.html",
        "https://www.pharmacity.vn/mediplantex-khong-co.html", 
        "https://www.pharmacity.vn/thuoc-mediplantex.html",
        # Thêm URL khác nếu cần
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    for i, url in enumerate(test_urls):
        print(f"\n{'='*80}")
        print(f"TEST {i+1}: {url}")
        
        try:
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                product_soup = BeautifulSoup(response.content, 'html.parser')
                
                # Tì<PERSON> tất cả text có chứa P17386
                page_text = product_soup.get_text()
                if "P17386" in page_text:
                    print("✅ Tìm thấy P17386 trong trang!")
                    
                    # Tìm context xung quanh P17386
                    import re
                    matches = []
                    for match in re.finditer(r'P17386', page_text):
                        start = max(0, match.start() - 50)
                        end = min(len(page_text), match.end() + 50)
                        context = page_text[start:end].replace('\n', ' ').replace('\t', ' ')
                        context = re.sub(r'\s+', ' ', context)
                        matches.append(context)
                    
                    print("📄 Context xung quanh P17386:")
                    for j, context in enumerate(matches):
                        print(f"   {j+1}. {context}")
                    
                    # Test logic extraction
                    product_code = "Không có"
                    
                    # Phương pháp 1: Tìm span
                    all_spans = product_soup.find_all('span')
                    for span in all_spans:
                        span_text = span.get_text(strip=True)
                        if re.match(r'^P\d{5,6}$', span_text):
                            product_code = span_text
                            print(f"✅ Tìm thấy trong span: {product_code}")
                            break
                    
                    # Phương pháp 2: Tìm gần thương hiệu
                    if product_code == "Không có":
                        brand_link = product_soup.find('a', href=lambda x: x and '/thuong-hieu/' in x)
                        if brand_link:
                            parent = brand_link.parent
                            if parent:
                                parent_text = parent.get_text()
                                code_match = re.search(r'P\d{5,6}', parent_text)
                                if code_match:
                                    product_code = code_match.group()
                                    print(f"✅ Tìm thấy gần thương hiệu: {product_code}")
                    
                    print(f"🎯 Mã sản phẩm: {product_code}")
                    break
                else:
                    print("❌ Không tìm thấy P17386")
            else:
                print(f"❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ Lỗi: {str(e)}")
    
    # Nếu không tìm thấy URL chính xác, thử search
    print(f"\n{'='*80}")
    print("🔍 Thử tìm sản phẩm có mã P17386 từ danh mục...")
    
    try:
        # Thử tìm từ trang danh mục
        category_url = "https://www.pharmacity.vn/thuoc-khong-ke-don"
        response = requests.get(category_url, headers=headers)
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            page_text = soup.get_text()
            
            if "P17386" in page_text:
                print("✅ Tìm thấy P17386 trong danh mục!")
                
                # Tìm link sản phẩm có chứa P17386
                all_links = soup.find_all('a', href=True)
                for link in all_links:
                    href = link.get('href')
                    link_text = link.get_text()
                    if 'P17386' in link_text or 'P17386' in href:
                        print(f"🔗 Link có P17386: {href}")
                        print(f"   Text: {link_text}")
            else:
                print("❌ Không tìm thấy P17386 trong danh mục")
        
    except Exception as e:
        print(f"❌ Lỗi khi tìm trong danh mục: {str(e)}")

if __name__ == "__main__":
    test_specific_product_code()
