import requests
from bs4 import BeautifulSoup
import re

def debug_price_unit(url):
    """Debug việc lấy giá và đơn vị"""
    print(f"🔍 Debug: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        page_text = soup.get_text()
        
        print(f"📄 Tìm kiếm tất cả pattern giá...")
        
        # Tìm tất cả pattern có chứa ₫
        all_prices = re.findall(r'\d+[.,]?\d*\s*₫[^,\s]*', page_text)
        print(f"🔍 Tất cả giá tìm thấy:")
        for i, price in enumerate(all_prices[:10]):  # Chỉ hiển thị 10 đầu tiên
            print(f"  {i+1}. {repr(price)}")
        
        # Tìm pattern có /Kit
        kit_patterns = re.findall(r'\d+[.,]?\d*\s*₫[^,\s]*Kit[^,\s]*', page_text)
        print(f"\n🎯 Pattern có 'Kit':")
        for i, pattern in enumerate(kit_patterns):
            print(f"  {i+1}. {repr(pattern)}")
        
        # Test các regex pattern
        patterns_to_test = [
            r'(\d+\.?\d*)\s*₫/(Kit|Hộp|Chai|Lọ|Vỉ|Gói|Bộ|Tuýp)(?![a-zA-Z])',
            r'(\d+\.?\d*)\s*₫\s*/\s*(Kit|Hộp|Chai|Lọ|Vỉ|Gói|Bộ|Tuýp)(?![a-zA-Z])',
            r'(\d+[.,]?\d*)\s*₫/(Kit)',
            r'(\d+[.,]?\d*)\s*₫\s*/\s*(Kit)',
            r'(\d+[.,]?\d*)\s*₫/Kit',
        ]
        
        print(f"\n🧪 Test các regex pattern:")
        for i, pattern in enumerate(patterns_to_test):
            match = re.search(pattern, page_text)
            if match:
                print(f"  ✅ Pattern {i+1}: {pattern}")
                print(f"     Match: {match.groups()}")
            else:
                print(f"  ❌ Pattern {i+1}: {pattern}")
        
        # Tìm text xung quanh "86.000"
        context_match = re.search(r'.{50}86\.000.{50}', page_text)
        if context_match:
            print(f"\n📝 Context xung quanh '86.000':")
            print(f"   {repr(context_match.group(0))}")
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")

if __name__ == "__main__":
    debug_price_unit("https://www.pharmacity.vn/genesign-test-respiratory-combo-antigen-rapid-20-test-hop.html")
