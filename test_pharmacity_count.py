from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import time

def test_pharmacity_product_count():
    """Test số lượng sản phẩm trên trang Pharmacity"""
    
    print("🚀 Khởi tạo Chrome browser...")
    service = Service(ChromeDriverManager().install())
    
    # Cấu hình Chrome options
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--log-level=3")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.maximize_window()
    
    try:
        # URL test
        test_url = "https://www.pharmacity.vn/duoc-pham?utm_source=web&utm_medium=subheader&utm_campaign=thuoc"
        print(f"🔍 Đang test URL: {test_url}")
        
        driver.get(test_url)
        time.sleep(5)
        
        print("\n📊 BƯỚC 1: Đếm sản phẩm ban đầu")
        initial_products = driver.find_elements(By.CLASS_NAME, "product-card")
        print(f"   Số product-card ban đầu: {len(initial_products)}")
        
        print("\n🔄 BƯỚC 2: Click 'Xem thêm' liên tục...")
        click_count = 0
        max_clicks = 50  # Giới hạn để tránh vòng lặp vô tận
        
        while click_count < max_clicks:
            try:
                # Scroll xuống cuối trang
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                
                # Đếm product-card hiện tại
                current_products = driver.find_elements(By.CLASS_NAME, "product-card")
                current_count = len(current_products)
                
                print(f"   Lần {click_count + 1}: {current_count} product-card")
                
                # Tìm nút "Xem thêm"
                try:
                    load_more_btn = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, "//button//span[text()='Xem thêm']"))
                    )
                    
                    if load_more_btn and load_more_btn.is_displayed():
                        # Scroll đến nút
                        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", load_more_btn)
                        time.sleep(1)
                        
                        # Click nút
                        try:
                            load_more_btn.click()
                            print(f"      ✅ Đã click 'Xem thêm'")
                            click_count += 1
                            time.sleep(4)  # Chờ load sản phẩm mới
                        except:
                            # Thử click bằng JavaScript
                            driver.execute_script("arguments[0].click();", load_more_btn)
                            print(f"      ✅ Đã click 'Xem thêm' bằng JS")
                            click_count += 1
                            time.sleep(4)
                    else:
                        print(f"      🛑 Nút 'Xem thêm' không hiển thị - Đã hết sản phẩm")
                        break
                        
                except TimeoutException:
                    print(f"      🛑 Không tìm thấy nút 'Xem thêm' - Đã hết sản phẩm")
                    break
                except Exception as e:
                    print(f"      🛑 Lỗi khi tìm nút 'Xem thêm': {e}")
                    break
                    
            except Exception as e:
                print(f"      ❌ Lỗi: {e}")
                break
        
        print(f"\n📊 BƯỚC 3: Kết quả cuối cùng")
        final_products = driver.find_elements(By.CLASS_NAME, "product-card")
        final_count = len(final_products)
        
        print(f"   ✅ Tổng số lần click 'Xem thêm': {click_count}")
        print(f"   ✅ Tổng số product-card cuối cùng: {final_count}")
        
        # Kiểm tra số lượng unique links
        print(f"\n🔗 BƯỚC 4: Kiểm tra unique links")
        unique_links = set()
        
        for i, card in enumerate(final_products):
            try:
                product_link = card.find_element(By.TAG_NAME, "a")
                product_url = product_link.get_attribute("href")
                
                if product_url:
                    # Loại bỏ query parameters
                    if "?" in product_url:
                        product_url = product_url.split("?")[0]
                    unique_links.add(product_url)
                    
            except Exception as e:
                print(f"      ⚠️ Product-card {i+1} không có link: {e}")
                continue
        
        print(f"   ✅ Số lượng unique links: {len(unique_links)}")
        
        # So sánh
        if final_count == len(unique_links):
            print(f"   ✅ PERFECT: Số product-card = Số unique links")
        else:
            print(f"   ⚠️ WARNING: Số product-card ({final_count}) ≠ Số unique links ({len(unique_links)})")
        
        print(f"\n" + "="*60)
        print(f"🎯 KẾT QUẢ TEST CUỐI CÙNG")
        print(f"="*60)
        print(f"📄 URL: {test_url}")
        print(f"🔄 Số lần click 'Xem thêm': {click_count}")
        print(f"📦 Tổng số product-card: {final_count}")
        print(f"🔗 Tổng số unique links: {len(unique_links)}")
        print(f"="*60)
        
        return final_count, len(unique_links), click_count
        
    except Exception as e:
        print(f"❌ Lỗi tổng quát: {e}")
        return 0, 0, 0
        
    finally:
        driver.quit()

if __name__ == "__main__":
    test_pharmacity_product_count()
