# ===== IMAGE DOWNLOAD UTILITIES =====

import os
import requests
import uuid
from urllib.parse import urlparse
from config.settings import IMAGE_DOWNLOAD_PATH, IMAGE_URL_PREFIX, ensure_directory_exists

def download_image(image_url, save_directory=None):
    """Download ảnh từ URL và trả về tên file"""
    if save_directory is None:
        save_directory = IMAGE_DOWNLOAD_PATH
        
    try:
        if not image_url or image_url.strip() == "":
            return None
            
        # Tạo tên file unique
        file_extension = ".png"  # Mặc định .png
        try:
            parsed_url = urlparse(image_url)
            original_extension = os.path.splitext(parsed_url.path)[1]
            if original_extension in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
                file_extension = original_extension
        except:
            pass
            
        filename = f"{uuid.uuid4().hex}{file_extension}"
        file_path = os.path.join(save_directory, filename)
        
        # Tạo thư mục nếu chưa tồn tại
        ensure_directory_exists(save_directory)
        
        # Download ảnh
        response = requests.get(image_url, timeout=30)
        response.raise_for_status()
        
        with open(file_path, 'wb') as f:
            f.write(response.content)
            
        print(f"  📥 Downloaded: {filename}")
        return filename
        
    except Exception as e:
        print(f"  ❌ Lỗi download ảnh {image_url}: {e}")
        return None

def get_image_path_for_db(filename):
    """Tạo path để lưu vào database"""
    if filename:
        return f"{IMAGE_URL_PREFIX}{filename}"
    return None

def download_and_get_db_path(image_url, save_directory=None):
    """Download ảnh và trả về path để lưu vào DB"""
    filename = download_image(image_url, save_directory)
    return get_image_path_for_db(filename)
