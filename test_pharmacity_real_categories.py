from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import re

def init_browser():
    """Khởi tạo browser với cấu hình tối ưu"""
    print("🚀 Khởi tạo Chrome browser...")
    service = Service(ChromeDriverManager().install())
    
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--disable-images")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--log-level=3")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    print("✅ Chrome browser đã sẵn sàng!")
    return driver

def test_specific_category_urls(driver):
    """Test các URL category cụ thể dựa trên cấu trúc thực tế của Pharmacity"""
    print(f"\n{'='*80}")
    print(f"🔍 TEST CÁC CATEGORY CỤ THỂ CỦA PHARMACITY")
    print(f"{'='*80}")
    
    # Danh sách các category thực tế từ Pharmacity
    real_categories = [
        # Category chính: Thuốc không kê đơn
        ("Thuốc không kê đơn", "https://www.pharmacity.vn/thuoc-khong-ke-don"),
        ("Thuốc ngừa thai", "https://www.pharmacity.vn/thuoc-ngua-thai"),
        ("Thuốc kháng dị ứng", "https://www.pharmacity.vn/thuoc-khang-di-ung"),
        ("Thuốc kháng viêm", "https://www.pharmacity.vn/thuoc-khang-viem"),
        ("Thuốc cảm lạnh", "https://www.pharmacity.vn/thuoc-cam-lanh"),
        ("Thuốc giảm cân", "https://www.pharmacity.vn/thuoc-giam-can"),
        ("Thuốc mắt/tai/mũi", "https://www.pharmacity.vn/thuoc-mattaimui"),
        ("Thuốc tiêu hóa", "https://www.pharmacity.vn/thuoc-tieu-hoa"),
        ("Thuốc dành cho nam", "https://www.pharmacity.vn/thuoc-danh-cho-nam"),
        ("Giảm đau hạ sốt", "https://www.pharmacity.vn/giam-dau-ha-sot"),
        ("Thuốc da liễu", "https://www.pharmacity.vn/thuoc-da-lieu"),
        ("Thuốc dành cho phụ nữ", "https://www.pharmacity.vn/thuoc-danh-cho-phu-nu"),
        ("Thuốc thần kinh", "https://www.pharmacity.vn/thuoc-than-kinh"),
        ("Thuốc cơ xương khớp", "https://www.pharmacity.vn/thuoc-co-xuong-khop"),
        ("Dầu gió dầu cù là", "https://www.pharmacity.vn/dau-gio-dau-cu-la"),
        ("Thuốc khác", "https://www.pharmacity.vn/thuoc-khac"),
        
        # Category chính: Thuốc kê đơn
        ("Thuốc kê đơn", "https://www.pharmacity.vn/thuoc-ke-don"),
        
        # Category chính: Thuốc khác
        ("Thuốc khác 1", "https://www.pharmacity.vn/thuoc-khac-1"),
        
        # Category chính: Vitamin & Thực phẩm chức năng
        ("Vitamin & Thực phẩm chức năng", "https://www.pharmacity.vn/thuoc-vitamin-thuc-pham-chuc-nang"),
        ("Vitamin và khoáng chất", "https://www.pharmacity.vn/vitamin-va-khoang-chat"),
        
        # Thêm các category khác
        ("Thực phẩm chức năng", "https://www.pharmacity.vn/thuc-pham-chuc-nang"),
        ("Mẹ và bé", "https://www.pharmacity.vn/me-va-be"),
        ("Chăm sóc cá nhân", "https://www.pharmacity.vn/cham-soc-ca-nhan"),
        ("Chăm sóc sắc đẹp", "https://www.pharmacity.vn/cham-soc-sac-dep"),
        ("Thiết bị y tế", "https://www.pharmacity.vn/thiet-bi-y-te-2"),
    ]
    
    all_product_urls = set()
    category_results = []
    
    for category_name, category_url in real_categories:
        try:
            print(f"\n🏷️ Đang test: {category_name}")
            print(f"📄 URL: {category_url}")
            
            driver.get(category_url)
            time.sleep(5)
            
            soup = BeautifulSoup(driver.page_source, "html.parser")
            
            # Tìm sản phẩm bằng nhiều selector khác nhau
            product_links = []
            
            # Selector 1: Tìm link có .html
            html_links = soup.find_all("a", href=True)
            for link in html_links:
                href = link.get("href")
                if href and ".html" in href:
                    if href.startswith("/"):
                        product_url = "https://www.pharmacity.vn" + href
                    elif href.startswith("https://www.pharmacity.vn"):
                        product_url = href
                    else:
                        continue
                    
                    # Loại bỏ query parameters
                    if "?" in product_url:
                        product_url = product_url.split("?")[0]
                    
                    # Kiểm tra xem có phải sản phẩm thực sự không
                    if any(keyword in product_url for keyword in ['/thuoc/', '/thuc-pham-chuc-nang/', '/me-va-be/', '/cham-soc-', '/thiet-bi-y-te/']):
                        if product_url not in product_links:
                            product_links.append(product_url)
            
            # Selector 2: Tìm theo class product-card
            product_cards = soup.find_all(class_="product-card")
            for card in product_cards:
                link = card.find("a", href=True)
                if link:
                    href = link.get("href")
                    if href:
                        if href.startswith("/"):
                            product_url = "https://www.pharmacity.vn" + href
                        elif href.startswith("https://www.pharmacity.vn"):
                            product_url = href
                        else:
                            continue
                        
                        if "?" in product_url:
                            product_url = product_url.split("?")[0]
                        
                        if product_url not in product_links:
                            product_links.append(product_url)
            
            print(f"  📊 Tìm thấy {len(product_links)} sản phẩm")
            
            # Hiển thị vài ví dụ
            if product_links:
                print(f"  📋 Ví dụ sản phẩm:")
                for i, url in enumerate(product_links[:5]):
                    print(f"    {i+1}. {url}")
            
            # Thêm vào tổng kết
            all_product_urls.update(product_links)
            category_results.append((category_name, len(product_links), product_links[:5]))
            
        except Exception as e:
            print(f"  ❌ Lỗi: {e}")
            category_results.append((category_name, 0, []))
    
    return category_results, all_product_urls

def test_search_functionality(driver):
    """Test chức năng tìm kiếm để tìm thêm sản phẩm"""
    print(f"\n{'='*80}")
    print(f"🔍 TEST CHỨC NĂNG TÌM KIẾM")
    print(f"{'='*80}")
    
    search_keywords = [
        "paracetamol", "vitamin", "thuốc ho", "thuốc đau đầu", 
        "kháng sinh", "thuốc dạ dày", "thuốc cảm cúm", "thuốc ngủ",
        "thuốc tim", "thuốc huyết áp", "insulin", "aspirin"
    ]
    
    search_products = set()
    
    for keyword in search_keywords[:5]:  # Test 5 từ khóa đầu tiên
        try:
            print(f"\n🔍 Tìm kiếm: '{keyword}'")
            
            # Thử URL tìm kiếm
            search_url = f"https://www.pharmacity.vn/search?q={keyword}"
            driver.get(search_url)
            time.sleep(5)
            
            soup = BeautifulSoup(driver.page_source, "html.parser")
            
            # Tìm sản phẩm trong kết quả tìm kiếm
            product_links = []
            all_links = soup.find_all("a", href=True)
            
            for link in all_links:
                href = link.get("href")
                if href and ".html" in href:
                    if href.startswith("/"):
                        product_url = "https://www.pharmacity.vn" + href
                    elif href.startswith("https://www.pharmacity.vn"):
                        product_url = href
                    else:
                        continue
                    
                    if "?" in product_url:
                        product_url = product_url.split("?")[0]
                    
                    if product_url not in product_links:
                        product_links.append(product_url)
            
            print(f"  📊 Tìm thấy {len(product_links)} sản phẩm")
            search_products.update(product_links)
            
            if product_links:
                print(f"  📋 Ví dụ:")
                for i, url in enumerate(product_links[:3]):
                    print(f"    {i+1}. {url}")
            
        except Exception as e:
            print(f"  ❌ Lỗi tìm kiếm '{keyword}': {e}")
    
    return search_products

def main():
    driver = init_browser()
    
    try:
        # 1. Test các category cụ thể
        category_results, category_products = test_specific_category_urls(driver)
        
        # 2. Test tìm kiếm
        search_products = test_search_functionality(driver)
        
        # 3. Tổng hợp kết quả
        all_unique_products = category_products.union(search_products)
        
        print(f"\n{'='*80}")
        print(f"📊 TỔNG KẾT PHARMACITY THỰC TẾ")
        print(f"{'='*80}")
        
        print(f"\n📋 KẾT QUẢ THEO CATEGORY:")
        total_category_products = 0
        for name, count, examples in category_results:
            print(f"  {name}: {count} sản phẩm")
            total_category_products += count
        
        print(f"\n📊 THỐNG KÊ TỔNG QUÁT:")
        print(f"🏷️ Tổng số categories đã test: {len(category_results)}")
        print(f"📦 Tổng sản phẩm từ categories: {len(category_products)}")
        print(f"🔍 Tổng sản phẩm từ tìm kiếm: {len(search_products)}")
        print(f"🎯 Tổng sản phẩm unique: {len(all_unique_products)}")
        print(f"📈 Tổng sản phẩm (có trùng lặp): {total_category_products}")
        
        print(f"\n🎯 KẾT LUẬN:")
        if len(all_unique_products) > 1000:
            print(f"✅ Pharmacity có {len(all_unique_products)} sản phẩm - RẤT NHIỀU, phù hợp crawl!")
        elif len(all_unique_products) > 500:
            print(f"✅ Pharmacity có {len(all_unique_products)} sản phẩm - Khá nhiều, phù hợp crawl!")
        elif len(all_unique_products) > 200:
            print(f"⚠️ Pharmacity có {len(all_unique_products)} sản phẩm - Vừa phải")
        else:
            print(f"❌ Pharmacity chỉ có {len(all_unique_products)} sản phẩm - Ít")
        
        # Top categories có nhiều sản phẩm nhất
        print(f"\n🏆 TOP CATEGORIES CÓ NHIỀU SẢN PHẨM NHẤT:")
        sorted_categories = sorted(category_results, key=lambda x: x[1], reverse=True)
        for i, (name, count, examples) in enumerate(sorted_categories[:10]):
            if count > 0:
                print(f"  {i+1}. {name}: {count} sản phẩm")
        
        # Lưu kết quả
        print(f"\n💾 Lưu danh sách {len(all_unique_products)} URL sản phẩm...")
        with open("pharmacity_real_products.txt", "w", encoding="utf-8") as f:
            for url in sorted(all_unique_products):
                f.write(url + "\n")
        print(f"✅ Đã lưu vào file: pharmacity_real_products.txt")
        
        # Lưu chi tiết theo category
        with open("pharmacity_category_details.txt", "w", encoding="utf-8") as f:
            f.write("PHARMACITY CATEGORY ANALYSIS\n")
            f.write("="*50 + "\n\n")
            for name, count, examples in category_results:
                f.write(f"{name}: {count} sản phẩm\n")
                if examples:
                    f.write("Ví dụ:\n")
                    for url in examples:
                        f.write(f"  - {url}\n")
                f.write("\n")
        print(f"✅ Đã lưu chi tiết vào file: pharmacity_category_details.txt")
        
    except Exception as e:
        print(f"❌ Lỗi tổng quát: {e}")
    
    finally:
        driver.quit()
        print("🎉 Hoàn thành phân tích thực tế Pharmacity!")

if __name__ == "__main__":
    main()
