# ===== BASE WORKFLOW CLASS =====

from abc import ABC, abstractmethod

class BaseWorkflow(ABC):
    """Base class cho tất cả workflows"""
    
    def __init__(self, connection, config):
        self.connection = connection
        self.config = config
        self.table_name = config['table_name']
        
        # Statistics
        self.total_success = 0
        self.total_failed = 0
        self.total_skipped = 0
        self.processed_items = set()
    
    @abstractmethod
    def process_data_rows(self, data_rows):
        """Xử lý danh sách dòng dữ liệu
        
        Args:
            data_rows: <PERSON>h sách dòng dữ liệu từ Google Sheets
        """
        pass
    
    def print_workflow_header(self):
        """In header của workflow"""
        print(f"🚀 BẮT ĐẦU WORKFLOW: {self.__class__.__name__}")
        print(f"📋 Table: {self.table_name}")
    
    def print_workflow_results(self):
        """In kết quả workflow"""
        print(f"\n📊 KẾT QUẢ WORKFLOW {self.__class__.__name__}:")
        print(f"  ✅ Thành công: {self.total_success}")
        print(f"  ⏭️ Bỏ qua: {self.total_skipped}")
        print(f"  ❌ Thất bại: {self.total_failed}")
    
    def is_duplicate(self, unique_key):
        """Kiểm tra duplicate"""
        if unique_key in self.processed_items:
            return True
        self.processed_items.add(unique_key)
        return False
    
    def execute_sql(self, sql, values):
        """Thực thi SQL với error handling"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql, values)
                return cursor.lastrowid
        except Exception as e:
            print(f"  ❌ Lỗi SQL: {e}")
            raise e
    
    def commit_transaction(self):
        """Commit transaction"""
        try:
            self.connection.commit()
        except Exception as e:
            print(f"  ❌ Lỗi commit: {e}")
            raise e
    
    def rollback_transaction(self):
        """Rollback transaction"""
        try:
            self.connection.rollback()
        except Exception as e:
            print(f"  ❌ Lỗi rollback: {e}")
            pass
