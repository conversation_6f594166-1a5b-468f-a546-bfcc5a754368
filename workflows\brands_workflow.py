# ===== BRANDS WORKFLOW =====

from workflows.base_workflow import BaseWorkflow
from processors.brands_processor import BrandsProcessor, AttributeOptionsProcessor

class BrandsWorkflow(BaseWorkflow):
    """Workflow đặc biệt cho brands - insert 2 bảng có quan hệ"""
    
    def __init__(self, connection, brands_config):
        super().__init__(connection, brands_config)
        
        # Tạo processors cho cả 2 bảng
        self.brands_processor = BrandsProcessor(brands_config)
        
        # Config cho attribute_options (hard-coded vì có quan hệ đặc biệt)
        attr_config = {
            'table_name': 'attribute_options',
            'columns': ["attribute_id", "admin_name", "sort_order", "swatch_value", "is_featured", "brand_image", "brand_description"],
            'mapping': {'admin_name': 1}
        }
        self.attr_processor = AttributeOptionsProcessor(attr_config)
    
    def process_data_rows(self, data_rows):
        """Xử lý workflow: attribute_options trước, sau đó brands với relationship"""
        self.print_workflow_header()
        print("📋 Workflow: attribute_options → brands (với relationship)")
        
        try:
            for row_index, row in enumerate(data_rows, start=1):
                try:
                    print(f"\n🔄 Xử lý dòng {row_index}:")
                    
                    # Bước 1: Xử lý dữ liệu cho attribute_options
                    attr_data = self.attr_processor.process_row(row)
                    if not attr_data:
                        print(f"  ⏭️ Bỏ qua dòng {row_index}: Dữ liệu không hợp lệ")
                        self.total_skipped += 1
                        continue
                    
                    brand_name = attr_data['admin_name']
                    
                    # Kiểm tra duplicate brand
                    if self.is_duplicate(brand_name):
                        print(f"  ⏭️ Bỏ qua dòng {row_index}: Brand '{brand_name}' đã tồn tại")
                        self.total_skipped += 1
                        continue
                    
                    # Bước 2: Insert vào attribute_options
                    attr_record = self.attr_processor.create_record_for_insert(attr_data)
                    attr_sql = self.attr_processor.get_insert_sql()
                    attribute_option_id = self.execute_sql(attr_sql, attr_record)
                    
                    print(f"  ✅ Inserted attribute_options: ID={attribute_option_id}, admin_name='{brand_name}'")
                    
                    # Bước 3: Xử lý dữ liệu cho brands (với attribute_option_id)
                    brand_data = self.brands_processor.process_row(row, attribute_option_id=attribute_option_id)
                    if not brand_data:
                        print(f"  ❌ Lỗi xử lý dữ liệu brands cho dòng {row_index}")
                        self.total_failed += 1
                        self.rollback_transaction()
                        continue
                    
                    # Bước 4: Insert vào brands
                    brand_record = self.brands_processor.create_record_for_insert(brand_data)
                    brand_sql = self.brands_processor.get_insert_sql()
                    brand_id = self.execute_sql(brand_sql, brand_record)
                    
                    print(f"  ✅ Inserted brands: ID={brand_id}, brand_name='{brand_data['brand_name']}', image='{brand_data['brand_image']}'")
                    
                    # Commit transaction cho từng dòng
                    self.commit_transaction()
                    self.total_success += 1
                    
                except Exception as e:
                    print(f"  ❌ Lỗi khi xử lý dòng {row_index}: {e}")
                    self.total_failed += 1
                    self.rollback_transaction()
                    continue
            
            # In kết quả
            self.print_workflow_results()
            print(f"  📋 Tổng cộng đã xử lý: {self.total_success * 2} records (attribute_options + brands)")
            
        except Exception as e:
            print(f"❌ Lỗi workflow: {e}")
            self.rollback_transaction()
