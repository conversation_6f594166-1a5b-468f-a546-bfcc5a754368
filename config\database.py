# ===== DATABASE CONFIGURATION =====

import pymysql

# MySQL config
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '',
    'database': 'bagisto',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def get_db_connection():
    """Tạo kết nối MySQL"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ Đã kết nối MySQL thành công!")
        return connection
    except Exception as e:
        print(f"❌ Lỗi kết nối MySQL: {e}")
        raise e

def check_table_exists(connection, table_name):
    """Kiểm tra table có tồn tại không"""
    check_table_sql = f"""
    SELECT COUNT(*) as count 
    FROM information_schema.tables 
    WHERE table_schema = '{DB_CONFIG['database']}' 
    AND table_name = '{table_name}'
    """
    
    with connection.cursor() as cursor:
        cursor.execute(check_table_sql)
        result = cursor.fetchone()
        return result['count'] > 0

def get_table_structure(connection, table_name):
    """Lấy cấu trúc table"""
    with connection.cursor() as cursor:
        cursor.execute(f"DESCRIBE {table_name}")
        return cursor.fetchall()

def get_table_count(connection, table_name):
    """Đếm số records trong table"""
    with connection.cursor() as cursor:
        cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
        result = cursor.fetchone()
        return result['count']

def get_sample_data(connection, table_name, limit=1):
    """Lấy mẫu dữ liệu từ table"""
    with connection.cursor() as cursor:
        cursor.execute(f"SELECT * FROM {table_name} LIMIT {limit}")
        return cursor.fetchall()
