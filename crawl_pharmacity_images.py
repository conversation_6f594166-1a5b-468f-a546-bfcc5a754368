from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import requests

# ===== CẤU HÌNH =====
MAX_ROWS = 60  # Số dòng tối đa muốn xử lý (thay đổi theo nhu cầu)

# HƯỚNG DẪN SỬ DỤNG START_ROW:
# - START_ROW = 2: Bắt đầu từ dòng 2 (đầu tiên)
# - START_ROW = 1127: Tiếp tục từ dòng 1127 (đã xử lý 1126 dòng)
START_ROW = 2  # Dòng bắt đầu xử lý

# Hàm kết nối Google Sheets
def connect_to_google_sheets():
    scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
    # Sử dụng file JSON mới
    creds = ServiceAccountCredentials.from_json_keyfile_name("medical-crawl-2024-013b6faaa588.json", scope)
    client = gspread.authorize(creds)
    # Sử dụng ID của Google Sheet từ URL
    sheet = client.open_by_key("1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8").worksheet("MomAndBaby")
    return sheet

def get_product_images(driver, url):
    """
    Lấy link ảnh sản phẩm từ URL
    """
    try:
        print(f"🔍 Đang truy cập: {url}")

        # Thử truy cập URL
        try:
            driver.get(url)
            time.sleep(3)  # Chờ trang load
        except Exception as e:
            print(f"❌ Không thể truy cập URL: {e}")
            return []

        # Kiểm tra nếu trang load thành công
        if "404" in driver.title.lower() or "not found" in driver.title.lower():
            print("❌ Trang không tồn tại (404)")
            return []

        soup = BeautifulSoup(driver.page_source, "html.parser")

        # Tìm phần chứa ảnh sản phẩm chính
        image_urls = []

        # Method 1: Tìm swiper chứa ảnh sản phẩm chính (product-media-slide)
        product_swiper = soup.find('div', class_=lambda x: x and 'product-media-slide' in str(x))

        if product_swiper:
            print("✅ Tìm thấy product-media-slide container")
            images = product_swiper.find_all('img')

            for img in images:
                src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
                if src:
                    # Chuyển đổi URL tương đối thành URL tuyệt đối
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = 'https://www.pharmacity.vn' + src

                    # Cải thiện pattern nhận diện ảnh sản phẩm - bao gồm timestamp
                    import re
                    # Pattern: P + 5 chữ số + _ + số, có thể có timestamp ở đầu
                    # Ví dụ: P00126_1.png, 20241107092031-0-P00198_1.png
                    has_product_code = bool(re.search(r'P\d{5}_\d+\.(png|jpg|jpeg|webp)', src, re.IGNORECASE))

                    if has_product_code and 'ecommerce' in src.lower():
                        if src not in image_urls:
                            image_urls.append(src)
                            print(f"  📸 Ảnh sản phẩm: {src}")

        # Method 2: Nếu không tìm thấy, tìm theo pattern mã sản phẩm
        if not image_urls:
            print("🔍 Tìm ảnh theo pattern mã sản phẩm...")
            all_images = soup.find_all('img')

            for img in all_images:
                src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
                if src:
                    # Chuyển đổi URL tương đối thành URL tuyệt đối
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = 'https://www.pharmacity.vn' + src

                    # Cải thiện pattern nhận diện ảnh sản phẩm - bao gồm timestamp
                    import re
                    # Pattern: P + 5 chữ số + _ + số, có thể có timestamp ở đầu
                    # Ví dụ: P00126_1.png, 20241107092031-0-P00198_1.png
                    has_product_code = bool(re.search(r'P\d{5}_\d+\.(png|jpg|jpeg|webp)', src, re.IGNORECASE))

                    if has_product_code and 'ecommerce' in src.lower():
                        # Bỏ qua ảnh không liên quan
                        skip_keywords = ['banner', 'badge', 'icon', 'logo', 'frame', 'button', 'static-website']
                        if not any(keyword in src.lower() for keyword in skip_keywords):
                            if src not in image_urls:
                                image_urls.append(src)
                                print(f"  📸 Ảnh sản phẩm: {src}")

        # Nếu không tìm thấy ảnh theo pattern, tìm tất cả ảnh trong swiper-slide
        if not image_urls:
            print("🔍 Không tìm thấy ảnh theo pattern, tìm tất cả ảnh trong swiper-slide...")

            # Tìm tất cả div có class chứa "swiper-slide"
            swiper_slides = soup.find_all('div', class_=lambda x: x and 'swiper-slide' in str(x))

            for slide in swiper_slides:
                # Tìm tất cả thẻ img trong slide này
                images_in_slide = slide.find_all('img')

                for img in images_in_slide:
                    src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
                    if src:
                        # Chuyển đổi URL tương đối thành URL tuyệt đối
                        if src.startswith('//'):
                            src = 'https:' + src
                        elif src.startswith('/'):
                            src = 'https://www.pharmacity.vn' + src

                        # Bỏ qua ảnh không liên quan
                        skip_keywords = ['banner', 'badge', 'icon', 'logo', 'frame', 'button', 'static-website']
                        if not any(keyword in src.lower() for keyword in skip_keywords):
                            if src not in image_urls:
                                image_urls.append(src)
                                print(f"  📸 Ảnh từ swiper-slide: {src}")

        # Lấy tất cả ảnh sản phẩm, không quan tâm đến kích thước
        final_images = image_urls

        # Giới hạn tối đa 5 ảnh
        final_images = final_images[:5]

        print(f"✅ Tìm thấy {len(final_images)} ảnh sản phẩm")
        return final_images

    except Exception as e:
        print(f"❌ Lỗi khi lấy ảnh từ {url}: {e}")
        return []

def main():
    # Khởi động trình duyệt
    print("Đang khởi tạo Chrome browser...")
    service = Service(ChromeDriverManager().install())
    
    # Cấu hình Chrome options
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    
    # Tắt logging để giảm lỗi console
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--log-level=3")
    
    driver = webdriver.Chrome(service=service, options=chrome_options)
    print("✅ Chrome browser đã sẵn sàng!")
    
    # Kết nối Google Sheets
    print("Đang kết nối Google Sheets...")
    sheet = connect_to_google_sheets()
    print("✅ Đã kết nối Google Sheets!")
    
    try:
        # Lấy dữ liệu từ cột A (URL) từ START_ROW đến MAX_ROWS+1
        end_row = min(START_ROW + MAX_ROWS - 1, MAX_ROWS + 1)
        print(f"Đang lấy dữ liệu từ dòng {START_ROW} đến {end_row}...")

        # Lấy URLs từ cột A
        url_range = f"A{START_ROW}:A{end_row}"
        urls = sheet.get(url_range)
        
        if not urls:
            print("❌ Không tìm thấy URL nào trong sheet!")
            return
        
        print(f"✅ Tìm thấy {len(urls)} URL để xử lý")
        print(f"🔧 Bắt đầu từ dòng {START_ROW}")

        # Xử lý từng URL
        for i, url_row in enumerate(urls):
            row_number = i + START_ROW  # Dòng thực tế trong sheet (bắt đầu từ START_ROW)
            cell_address = f"R{row_number}"

            # Kiểm tra nếu dòng trống hoặc không có URL
            if not url_row or not url_row[0] or not url_row[0].strip():
                print(f"\n⚠️ Dòng {row_number}: Ô trống - bỏ qua")
                # Ghi "Không có URL" vào cột R
                sheet.update(cell_address, [["Không có URL"]])
                time.sleep(0.5)
                continue

            url = url_row[0].strip()

            # Kiểm tra nếu URL hợp lệ
            if not url.startswith(('http://', 'https://')):
                print(f"\n⚠️ Dòng {row_number}: URL không hợp lệ - {url}")
                # Ghi "URL không hợp lệ" vào cột R
                sheet.update(cell_address, [["URL không hợp lệ"]])
                time.sleep(0.5)
                continue

            # Kiểm tra nếu URL thuộc Pharmacity
            if 'pharmacity.vn' not in url.lower():
                print(f"\n⚠️ Dòng {row_number}: Không phải URL Pharmacity - {url}")
                # Ghi "Không phải Pharmacity" vào cột R
                sheet.update(cell_address, [["Không phải Pharmacity"]])
                time.sleep(0.5)
                continue

            print(f"\n📋 Xử lý dòng {row_number}: {url}")

            # Lấy ảnh từ URL
            image_urls = get_product_images(driver, url)

            # Chuẩn bị dữ liệu để ghi vào cột R
            if image_urls:
                # Nối các URL ảnh bằng ký tự xuống dòng
                image_data = "\n".join(image_urls)
            else:
                image_data = "Không có ảnh"

            # Xóa dữ liệu cũ trong ô R và ghi dữ liệu mới
            print(f"📝 Ghi dữ liệu vào ô {cell_address}")

            # Xóa dữ liệu cũ trước
            try:
                sheet.update(cell_address, [[""]])
                time.sleep(1)

                # Ghi dữ liệu mới
                sheet.update(cell_address, [[image_data]])
                print(f"✅ Đã cập nhật {len(image_urls)} ảnh vào ô {cell_address}")
            except Exception as e:
                if "quota exceeded" in str(e).lower() or "429" in str(e):
                    print(f"⚠️ Quota exceeded, chờ 60 giây...")
                    time.sleep(60)
                    # Thử lại
                    sheet.update(cell_address, [[image_data]])
                    print(f"✅ Đã cập nhật (retry) {len(image_urls)} ảnh vào ô {cell_address}")
                else:
                    raise e

            # Chờ 2 giây giữa các request để tránh bị block
            time.sleep(1)
    
    except Exception as e:
        print(f"❌ Lỗi trong quá trình xử lý: {e}")
    
    finally:
        # Đóng trình duyệt
        driver.quit()
        print("\n🎉 Hoàn tất quá trình cập nhật ảnh!")

if __name__ == "__main__":
    main()
