import requests
from bs4 import BeautifulSoup
import re

def debug_classification_structure(url):
    """Debug cấu trúc phân loại sản phẩm từ label và div"""
    print(f"🔍 Debug cấu trúc phân loại từ: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Tìm thẻ label có text "Phân loại sản phẩm"
        classification_labels = soup.find_all('label', string=lambda text: text and 'Phân loại sản phẩm' in text)
        print(f"📄 Tìm thấy {len(classification_labels)} label có text 'Phân loại sản phẩm'")
        
        product_classifications = []
        
        for i, label in enumerate(classification_labels):
            print(f"\n--- Label thứ {i+1}: {repr(label.get_text())} ---")
            
            # Tìm div ngay sau label này
            next_div = label.find_next_sibling('div')
            if next_div:
                print(f"✅ Tìm thấy div ngay sau label")
                
                # Tìm tất cả button trong div này
                buttons = next_div.find_all('button')
                print(f"🔍 Tìm thấy {len(buttons)} button trong div")
                
                for j, button in enumerate(buttons):
                    print(f"  Button {j+1}:")
                    
                    # Tìm span trong button
                    spans = button.find_all('span')
                    print(f"    Tìm thấy {len(spans)} span")
                    
                    for k, span in enumerate(spans):
                        span_text = span.get_text(strip=True)
                        print(f"    Span {k+1}: {repr(span_text)}")
                        
                        # Lưu text span nếu không trống và không phải là số
                        if span_text and not span_text.isdigit() and span_text not in product_classifications:
                            product_classifications.append(span_text)
                            print(f"    ✅ Đã lưu: {span_text}")
            else:
                print(f"❌ Không tìm thấy div ngay sau label")
        
        # Nếu không tìm thấy bằng cách trên, thử tìm theo cấu trúc khác
        if not product_classifications:
            print(f"\n🔍 Thử tìm theo cấu trúc khác...")
            
            # Tìm tất cả div có chứa text "Phân loại sản phẩm"
            all_divs = soup.find_all('div')
            for div in all_divs:
                if 'Phân loại sản phẩm' in div.get_text():
                    print(f"📄 Tìm thấy div chứa 'Phân loại sản phẩm'")
                    
                    # Tìm button trong div này
                    buttons = div.find_all('button')
                    print(f"🔍 Tìm thấy {len(buttons)} button")
                    
                    for button in buttons:
                        spans = button.find_all('span')
                        for span in spans:
                            span_text = span.get_text(strip=True)
                            if span_text and not span_text.isdigit() and span_text not in product_classifications:
                                # Kiểm tra nếu là từ khóa phân loại hợp lệ
                                classification_keywords = ['Kit', 'Hộp', 'Chai', 'Lọ', 'Vỉ', 'Gói', 'Bộ', 'Tuýp', 'Viên', 'Túi']
                                if any(keyword.lower() in span_text.lower() for keyword in classification_keywords):
                                    product_classifications.append(span_text)
                                    print(f"✅ Tìm thấy phân loại: {span_text}")
        
        # Gộp tất cả phân loại
        if product_classifications:
            final_classification = ", ".join(product_classifications)
        else:
            final_classification = "Không có"
        
        print(f"\n📊 Kết quả phân loại sản phẩm: {final_classification}")
        return final_classification
        
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return "Không có"

if __name__ == "__main__":
    # Test với sản phẩm có nhiều phân loại
    test_urls = [
        "https://www.pharmacity.vn/siang-pure-oil-3cc-loc-6-chai.html",
        "https://www.pharmacity.vn/genesign-test-respiratory-combo-antigen-rapid-20-test-hop.html",
        # Thêm URL sản phẩm có cả Vỉ và Hộp nếu có
    ]
    
    for i, test_url in enumerate(test_urls):
        print(f"\n{'='*80}")
        print(f"TEST {i+1}")
        classification = debug_classification_structure(test_url)
        
        print(f"\n🎯 Phân loại cuối cùng: {classification}")
